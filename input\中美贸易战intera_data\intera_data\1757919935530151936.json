{"user_id": "1757919935530151936", "interactions_count": 47, "interactions": [{"conversation_id": "1909928511952781496", "tweet_id": "1909944451050488127", "timestamp": "2025-04-09T12:20:16+00:00", "timestamp_unix": 1744201216, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Slander doesn't make you taller.", "context": {"type": "tweet", "id": "1909942641606750689", "text": "@unknownruler8 @SkyNews 白皮垃圾马上就吃不起鸡蛋了。我要看着你们进地狱，kiss my ass，little shit", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 145}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909944485410111576", "timestamp": "2025-04-09T12:20:25+00:00", "timestamp_unix": 1744201225, "type": "comment", "text": "@jimjo070 @unknownruler8 @SkyNews No.", "context": {"type": "tweet", "id": "1910163883882610877", "text": "📣China’s tantrum proves <PERSON>’s tariffs hit where it hurts. America’s finally playing hardball... no more bending over for a regime that censors truth and steals jobs.", "author_id": "1657867787782479873", "author_username": "unknownruler8"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 88}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909946592406233223", "timestamp": "2025-04-09T12:28:47+00:00", "timestamp_unix": 1744201727, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews I don't need your fucking doodles buddy.", "context": {"type": "tweet", "id": "1909946263706927412", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 另外说一句，我183cm，不需要长高了，我对自己的身高感到满意。谢谢", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 74}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909947059806908778", "timestamp": "2025-04-09T12:30:38+00:00", "timestamp_unix": 1744201838, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Ma'am you're crying about me in two languages.", "context": {"type": "tweet", "id": "1909946969705070689", "text": "@<PERSON>_<PERSON><PERSON><PERSON><PERSON> @unknownruler8 @SkyNews Who tf cares about you.", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 60}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909947776869544073", "timestamp": "2025-04-09T12:33:29+00:00", "timestamp_unix": 1744202009, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews I also speak two languages just one of them isn't pictograms drawn buy thin wristed fished eyed mimetic automatons.", "context": {"type": "tweet", "id": "1910163883882610877", "text": "📣China’s tantrum proves <PERSON>’s tariffs hit where it hurts. America’s finally playing hardball... no more bending over for a regime that censors truth and steals jobs.", "author_id": "1657867787782479873", "author_username": "unknownruler8"}, "metrics": {"retweet_count": 0, "reply_count": 4, "like_count": 0, "quote_count": 0, "view_count": 54}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909948862426493040", "timestamp": "2025-04-09T12:37:48+00:00", "timestamp_unix": 1744202268, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews You're unaware that Asians look more like apes? Too busy eating puppies to take a moment to compare your diminutive grandfather's face to the local wildlife?", "context": {"type": "tweet", "id": "1909948213731451185", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 你的妈妈是跟大猩猩生育了你还是，你爸操了一只猴子？让你看起来真的让人悲伤，照照镜子看看的样子，你妈都为你感到抱歉。哦，不好意思104%的关税在你身上，你甚至都负担不起。", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909949153674760379", "timestamp": "2025-04-09T12:38:58+00:00", "timestamp_unix": 1744202338, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Did you make a Muslim slave write this post and sell it to my well intentioned wife over temu?", "context": {"type": "tweet", "id": "1909948438453846446", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 象形文字？嗯嗯当然，以你15分的智商，当然看不懂象形文字。", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909949795109572812", "timestamp": "2025-04-09T12:41:30+00:00", "timestamp_unix": 1744202490, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Great so you are aware of the ape like features of many Asian peoples. Possibly you just misunderstood that you were saying.", "context": {"type": "tweet", "id": "1909949569003364749", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 看看你一身的毛和你肮脏的体味。那是与猩猩最直接的相关，我担心你买不起除臭剂，傻逼", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909950484309324029", "timestamp": "2025-04-09T12:44:15+00:00", "timestamp_unix": 1744202655, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Hey dog eater, Asians love rice so much often the noun for eating/food is the same as rice and they even protect their markets from superior rice growers like the tall, elevated, civilization setters known as the white man.\n\nDo you eat cats too or do they help you hunt the bugs?", "context": {"type": "tweet", "id": "1909949888865206675", "text": "@<PERSON>_<PERSON><PERSON>tar<PERSON> @unknownruler8 @SkyNews Who tf cares about rice, stinky monkey?", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 55}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909950670817403217", "timestamp": "2025-04-09T12:44:59+00:00", "timestamp_unix": 1744202699, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews I said no foutune cookie, they're too sugary. Still, what were my lucky numbers?", "context": {"type": "tweet", "id": "1909950277756899537", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 哦，你不是猩猩，你是你妈和猩猩的孩子，所以，一半猩猩，所以你看起来像个人，只是散发恶臭味。", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909950879450411210", "timestamp": "2025-04-09T12:45:49+00:00", "timestamp_unix": 1744202749, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews My god now you're threatening to eat more dogs if I keep responding to your lame rote version of western quips?", "context": {"type": "tweet", "id": "1909950555885342874", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 你好心的妻子？目前正在我的床上，她说她厌倦了你的恶臭", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909951617568227376", "timestamp": "2025-04-09T12:48:45+00:00", "timestamp_unix": 1744202925, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews You're so far off proper slander you're now suggesting a western woman sees one redeeming quality in effete dead eyed yellowed half-men whi insist they're real boys too.", "context": {"type": "tweet", "id": "1909951272599584771", "text": "@<PERSON>_<PERSON><PERSON><PERSON><PERSON> @unknownruler8 @SkyNews But your wife said I was great", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 5, "like_count": 0, "quote_count": 0, "view_count": 36}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909952447272931390", "timestamp": "2025-04-09T12:52:03+00:00", "timestamp_unix": 1744203123, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews No, no, I said egg roll these are spring rolls you fucking monkey and get those crickets out of there that's literally an insect.", "context": {"type": "tweet", "id": "1909952187087880282", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 如果我是中国餐厅的老板，我就把你的骨灰给你妈煲饭吃", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909952643109106104", "timestamp": "2025-04-09T12:52:49+00:00", "timestamp_unix": 1744203169, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews As I said I will not make amends with you over dog meat dumplings so quit asking", "context": {"type": "tweet", "id": "1909952404503818470", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 我有过黑人女朋友，现在的女朋友是白人，所以真遗憾哈哈。继续哭泣，我不在意", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909952989193810036", "timestamp": "2025-04-09T12:54:12+00:00", "timestamp_unix": 1744203252, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews No I will not send you my companies internal emails.", "context": {"type": "tweet", "id": "1909952784969146846", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 没问题，我这就把他们塞进你老婆的屁眼里", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909953071871844374", "timestamp": "2025-04-09T12:54:32+00:00", "timestamp_unix": 1744203272, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Did I just finish a final fantasy game?", "context": {"type": "tweet", "id": "1909952953777266719", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 谁在乎你的和解？我不在乎，我不和一个臭烘烘的原始人和解", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909953294392295480", "timestamp": "2025-04-09T12:55:25+00:00", "timestamp_unix": 1744203325, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews I have reported you to the Chinese government. They asked me if I could email them any blue prints for electronic devices and hung up on me.", "context": {"type": "tweet", "id": "1909953102306046164", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 你老婆说你软的像棉花糖", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 38}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909953504707301779", "timestamp": "2025-04-09T12:56:15+00:00", "timestamp_unix": 1744203375, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Ma'am you're asian.", "context": {"type": "tweet", "id": "1909953355126067668", "text": "@<PERSON>_<PERSON><PERSON><PERSON><PERSON> @unknownruler8 @SkyNews Who tf cares about u? Stinky ape?", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909953632168014036", "timestamp": "2025-04-09T12:56:45+00:00", "timestamp_unix": 1744203405, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews No I will not mail you a box full of salted golden retrievers and quit DMing about it", "context": {"type": "tweet", "id": "1909953204294648172", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 我会继续发送，谁在意你", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909953891497619869", "timestamp": "2025-04-09T12:57:47+00:00", "timestamp_unix": 1744203467, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews My lady you are a completely reactionary force to the doings of my people if you handy noticed. And I would inventory my physical form and have a think about who <PERSON> himself prefers.", "context": {"type": "tweet", "id": "1909953667148722367", "text": "@<PERSON>_<PERSON>eetardo @unknownruler8 @SkyNews Great, one more time, who tf cares what u did, stinky ape?", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909954133219565771", "timestamp": "2025-04-09T12:58:45+00:00", "timestamp_unix": 1744203525, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Surely you have better slander than this? This is child like -- show me that Chinese creativity they're not at all famous for you fished eyed robot.", "context": {"type": "tweet", "id": "1909953858182500678", "text": "@<PERSON>_<PERSON><PERSON><PERSON><PERSON> @unknownruler8 @SkyNews Who tf cares? Stinky ape", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909954233262080338", "timestamp": "2025-04-09T12:59:09+00:00", "timestamp_unix": 1744203549, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews I tend to doodle war scenes or flowers but these nonsense lines are pretty neat.", "context": {"type": "tweet", "id": "1909954033751834631", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 当你的骨灰成为你妈妈的美式拿铁，你爸爸会开心", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909954407271170509", "timestamp": "2025-04-09T12:59:50+00:00", "timestamp_unix": 1744203590, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Seethe diminutive little Asian woman. How are the feet, are they small?", "context": {"type": "tweet", "id": "1909954224387232173", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 谁在意你？女士？你妈妈说我很棒。", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909954806627606584", "timestamp": "2025-04-09T13:01:25+00:00", "timestamp_unix": 1744203685, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews So, no, you don't. Sad. Just copy some of mine but avoid the ones where I mock the entire Asian countenance -- as we are short little dead eyed robots who eat everything not nailed down like some sort of intergalactic maggot.", "context": {"type": "tweet", "id": "1909954468462084115", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 这是你的最好的创意了吗？太弱了？难道你是斯德哥尔摩综合征？", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909954871169626376", "timestamp": "2025-04-09T13:01:41+00:00", "timestamp_unix": 1744203701, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews You think anyone believes that?", "context": {"type": "tweet", "id": "1910163883882610877", "text": "📣China’s tantrum proves <PERSON>’s tariffs hit where it hurts. America’s finally playing hardball... no more bending over for a regime that censors truth and steals jobs.", "author_id": "1657867787782479873", "author_username": "unknownruler8"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909955111217955199", "timestamp": "2025-04-09T13:02:38+00:00", "timestamp_unix": 1744203758, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Sir, your blinkers still on.", "context": {"type": "tweet", "id": "1909954755683811671", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 女士？我是你爸爸", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909955356169470431", "timestamp": "2025-04-09T13:03:36+00:00", "timestamp_unix": 1744203816, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Get fggt keep responded are your weak little rice powered hands tired?", "context": {"type": "tweet", "id": "1909954821576351826", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 是的，在你妈妈身上", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909955518426206330", "timestamp": "2025-04-09T13:04:15+00:00", "timestamp_unix": 1744203855, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Actually you're in luck, got a fresh shipment of corgi faces in for tonight's dinner just this morning.", "context": {"type": "tweet", "id": "1909955283583115297", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 谁在意你的意见呢？浑身恶臭带着毛发，看起来像一只脏猩猩。你爸爸妈妈需要一个避孕套，而不是把你这种智商为0的家伙带到世界。当然我会为你主持葬礼，并且把你的骨灰放入你妈妈的拿铁里面", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909955728090996895", "timestamp": "2025-04-09T13:05:05+00:00", "timestamp_unix": 1744203905, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews No i won't circumcise them before selling without increasing the price. You're in here every week asking about that.", "context": {"type": "tweet", "id": "1909955516375371812", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 那就回家看看你妈妈的", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909956040629559789", "timestamp": "2025-04-09T13:06:20+00:00", "timestamp_unix": 1744203980, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Well I don't love you at all and I refuse to eat those chocolate covered mantis you keep sending me.", "context": {"type": "tweet", "id": "1909955757157871971", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 足够让你的妈妈过的性高潮", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909956133969703045", "timestamp": "2025-04-09T13:06:42+00:00", "timestamp_unix": 1744204002, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews I dunno,  let's say 4 for a dozen?", "context": {"type": "tweet", "id": "1909955911009075710", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 事实上你也很幸运，你的妈妈刚刚从我这里得到快乐", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909956316203794441", "timestamp": "2025-04-09T13:07:25+00:00", "timestamp_unix": 1744204045, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews And I refuse to do it, animal cruelty is wrong even if it does get them away from China.", "context": {"type": "tweet", "id": "1909955971604222338", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 因为你没有", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909956524111167815", "timestamp": "2025-04-09T13:08:15+00:00", "timestamp_unix": 1744204095, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Jokes on you I already knew the bat meat stew you sent me had covid in it.", "context": {"type": "tweet", "id": "1909956268976226365", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 谁在乎你吃不吃，你妈喜欢吃", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909956637110014108", "timestamp": "2025-04-09T13:08:42+00:00", "timestamp_unix": 1744204122, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews You know as well as I do that no one lies black people.", "context": {"type": "tweet", "id": "1909956378879467890", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 这就是你妈妈喜欢黑人的理由？", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909956994443751575", "timestamp": "2025-04-09T13:10:07+00:00", "timestamp_unix": 1744204207, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Agree they're terrible. Finally some common ground.", "context": {"type": "tweet", "id": "1909956906698895462", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 因为黑人和你都是智商更低的生物", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909957464033759375", "timestamp": "2025-04-09T13:11:59+00:00", "timestamp_unix": 1744204319, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Do you think I can't see the failings of China by just opening a book? You exist on modernity at our leisure. Your physical form is repulsive and your art is derivative.", "context": {"type": "tweet", "id": "1909957152548217135", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 这是你与他们的共同点，智商为0", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909957557604536498", "timestamp": "2025-04-09T13:12:21+00:00", "timestamp_unix": 1744204341, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews I'll do what I want, thanks.", "context": {"type": "tweet", "id": "1909957260610269509", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 你怎么好意思嘲笑别人呢，", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909959872642273518", "timestamp": "2025-04-09T13:21:33+00:00", "timestamp_unix": 1744204893, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Fresh out of crushed puppy heads marinated in the urine of children, you're going to have to eat a hamburger instead.", "context": {"type": "tweet", "id": "1909957734323343726", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 当然，猩猩总是随心所欲", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909961389076083150", "timestamp": "2025-04-09T13:27:35+00:00", "timestamp_unix": 1744205255, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews These are just one sentences attempts at attacks but all are common enough as to be said by an erudite American child. I'm mocking you plainly for eating dogs, being diminutive  and being derivative of a real civilization. You will need to try harder fish man.", "context": {"type": "tweet", "id": "1909960883926917488", "text": "@Ricky_<PERSON><PERSON><PERSON>do @unknownruler8 @SkyNews Why do you hang two dead mice under your armpits; your mother went through untold hardships to give birth to you but you didn't bring her even one moment of pride; even <PERSON> has abandoned you with a jealous face; the cheerleader in your school days ignored you", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909961711089586681", "timestamp": "2025-04-09T13:28:51+00:00", "timestamp_unix": 1744205331, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Are these just gay AI attempts? .", "context": {"type": "tweet", "id": "1909960998737600659", "text": "@Ricky_<PERSON>eetardo @unknownruler8 @SkyNews your colleague next door after work forgot you, the baby didn't want to enjoy your hug, the passerby on the cliff rejected your outstretched hand, so you can only rely on the empty online world to beg for attention, clown.", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909962144042406004", "timestamp": "2025-04-09T13:30:35+00:00", "timestamp_unix": 1744205435, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Ma'am my father has even better takes on zipperheads.", "context": {"type": "tweet", "id": "1909961937204760947", "text": "@Ricky_<PERSON>eetardo @unknownruler8 @SkyNews They are ur dad attempts", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909962339106848853", "timestamp": "2025-04-09T13:31:21+00:00", "timestamp_unix": 1744205481, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews Must be funnier when you got that fresh dog meat dinner high going.", "context": {"type": "tweet", "id": "1909962114300854498", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 谁在意一个，臭烘烘的白毛猩猩的攻击呢？这对我无效，来点新的。", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909962490940731431", "timestamp": "2025-04-09T13:31:57+00:00", "timestamp_unix": 1744205517, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews No, thats not why he calls them that at all.", "context": {"type": "tweet", "id": "1909962360623927562", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 你没有爸爸", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909962795837169949", "timestamp": "2025-04-09T13:33:10+00:00", "timestamp_unix": 1744205590, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews So we agree.", "context": {"type": "tweet", "id": "1909962680825528552", "text": "@Ricky_Reeetardo @unknownruler8 @SkyNews 谢谢，有机会我会尝试以下。但我对你妈妈的骨灰汉堡更感兴趣", "author_id": "1392475958247006212", "author_username": "nostalgialee1"}, "metrics": {"retweet_count": 0, "reply_count": 7, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1909928511952781496", "tweet_id": "1909965737587851632", "timestamp": "2025-04-09T13:44:51+00:00", "timestamp_unix": 1744206291, "type": "comment", "text": "@nostalgialee1 @unknownruler8 @SkyNews He blocked me, seems the Chinese spine is also small. Sad.", "context": {"type": "tweet", "id": "1909962795837169949", "text": "@nostalgialee1 @unknownruler8 @SkyNews So we agree.", "author_id": "1757919935530151936", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1912122258559992188", "tweet_id": "1912161373636751488", "timestamp": "2025-04-15T15:09:32+00:00", "timestamp_unix": 1744729772, "type": "comment", "text": "@nypost Thats basically just our criminal class", "context": {"type": "tweet", "id": "1919160654130229536", "text": "China might be producing cartoonish AI videos of Americans working in sweat shops, but it inspires an important debate.\n\nWhat would we prefer as Americans: over-worked laborers halfway across the world (many of which are children), or overweight workers here at home?\n\nWhich problem would we have more power to solve? Which country do you trust more to improve labor standards and ensure safe and healthy working environments?", "author_id": "1596268726227173378", "author_username": "CorbanQualls"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1915761693223010794", "tweet_id": "1916881479314874754", "timestamp": "2025-04-28T15:45:33+00:00", "timestamp_unix": **********, "type": "comment", "text": "@ny<PERSON>er @GreatBrandonsg2 @Ne_pas_couvrir @SpoxCHN_LinJian No one's going to take an effete woman-man who can't watch <PERSON> and <PERSON> without getting hungry seriously. Surly you can do that math you fish eyed automaton.", "context": {"type": "tweet", "id": "1918947762579677594", "text": "平常官方说话老严肃了，这次就一句“美方不要混淆视听”，搞得美国真就像个小孩子在胡闹似的", "author_id": "1857644352090288130", "author_username": "<PERSON>_<PERSON>_xing"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 37}}]}