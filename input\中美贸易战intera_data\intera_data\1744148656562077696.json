{"user_id": "1744148656562077696", "interactions_count": 19, "interactions": [{"conversation_id": "1908101332659327354", "tweet_id": "1908158733286617511", "timestamp": "2025-04-04T14:04:28+00:00", "timestamp_unix": 1743775468, "type": "comment", "text": "@thinking_panda We have a massive trade deficit with communist China, we must pull manufacturing from there and return it home,", "context": {"type": "tweet", "id": "1908743952930382022", "text": "34% is not high enough yet. We are hoping for more. The China-U.S. trade has turned into mutual dependence, which does not serve the best interests of either nation. This era must come to an end. Trade barriers are a first step, and the Taiwan invasion will complete the divorce.", "author_id": "866010904357253120", "author_username": "CaliforniaRifle"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 316}}, {"conversation_id": "1909450181235319202", "tweet_id": "1909529922793881953", "timestamp": "2025-04-08T08:53:05+00:00", "timestamp_unix": 1744102385, "type": "comment", "text": "@liqian_ren Ok, go take it if you can", "context": {"type": "tweet", "id": "1910411774538764384", "text": "So, it is already a solid 45% off👀", "author_id": "1234484021633179648", "author_username": "Jeytery1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 510}}, {"conversation_id": "1909975818425811174", "tweet_id": "1910041282933883047", "timestamp": "2025-04-09T18:45:03+00:00", "timestamp_unix": 1744224303, "type": "comment", "text": "@zhao_dashuai If I was president trump, just end the relationship with China, let them peddle there junk elsewhere", "context": {"type": "tweet", "id": "1910084690960134346", "text": "#25thAmendmentNow", "author_id": "1870491036100804608", "author_username": "Steffi742889"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 96}}, {"conversation_id": "1910617215281578164", "tweet_id": "1910659972851261752", "timestamp": "2025-04-11T11:43:30+00:00", "timestamp_unix": 1744371810, "type": "comment", "text": "@BarrettYouTube Been saying the U.S. and china are done,, and that’s good for the U.S..", "context": {"type": "tweet", "id": "1910965437216809184", "text": "This is the final test. China calls the Trump Bluff.", "author_id": "1589544502833782784", "author_username": "Fernand14784592"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 381}}, {"conversation_id": "1910637768067473830", "tweet_id": "1910761143943168487", "timestamp": "2025-04-11T18:25:31+00:00", "timestamp_unix": 1744395931, "type": "comment", "text": "@SpoxCH<PERSON>_Lin<PERSON><PERSON>, now can you shut up already?", "context": {"type": "tweet", "id": "1916549508680257791", "text": "#DPRK-Russia Treaty on Comprehensive Strategic Partnership (Article 4) • 20 June 2024\n\n#China is in-between #Russia and North Korea – https://t.co/ZPRhj3mDqQ", "author_id": "1829550629955706880", "author_username": "JohnGalt_AU"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1911082150163784150", "tweet_id": "1911190136563601912", "timestamp": "2025-04-12T22:50:11+00:00", "timestamp_unix": 1744498211, "type": "comment", "text": "@zhao_dashuai Since when do Chicoms care about anything that’s nor communist, before you spout, let me get the freedom loving Taiwanese on here 😉😉😉", "context": {"type": "tweet", "id": "1911934940373045625", "text": "@WallStreetMav Then turned the Ship around after The Apprentice FAFO’d Apple!\n\nApple’s <PERSON> explained Tariffs to the Real Estate Guy!\n\nOh! Oooops! My Bad! \n\nDon’t tell anybody!\n\nLOL 😂", "author_id": "1104136214813253632", "author_username": "EstoppelU"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 144}}, {"conversation_id": "1912205461908004882", "tweet_id": "1912326695195222426", "timestamp": "2025-04-16T02:06:28+00:00", "timestamp_unix": 1744769188, "type": "comment", "text": "@libsoftiktok Someone shut the Chicom peasant up!", "context": {"type": "tweet", "id": "1920873721591480442", "text": "@tedlieu https://t.co/4xvj784Y9s", "author_id": "1654194440804929536", "author_username": "WillWeltgeist"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1914828845171270014", "tweet_id": "1914839450934202747", "timestamp": "2025-04-23T00:31:15+00:00", "timestamp_unix": 1745368275, "type": "comment", "text": "@zhao_dashuai After a deal is reached, after a deal is reached, might want to listen carefully", "context": {"type": "tweet", "id": "1915110580240986395", "text": "China will never be to same as it was. Trump changed things and manufacturing is returning the the Rust Belt. Automation will claim many of the jobs but anyone that wants to work can now find jobs paying well.", "author_id": "1599598845486596096", "author_username": "DanScott1956"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 587}}, {"conversation_id": "1915146512033866101", "tweet_id": "1915160027977179460", "timestamp": "2025-04-23T21:45:07+00:00", "timestamp_unix": 1745444707, "type": "comment", "text": "@thecyrusjanssen Yeah we heard this before…", "context": {"type": "tweet", "id": "1915429947063361822", "text": "The Shift was happening.\nChina encouraged it.\nThe playing field was not level.\nThe “rules” were not fair.\n\n<PERSON> merely took a guerrilla attack upon the US Economy that was happening near silently behind the scenes and made it public with “Tariffs”\n\nHe essentially said “pick a side”, because they WANT this to escalate.\n\nChina could have easily calmed this situation. They chose escalation. Which is the proverbial proof in the pudding.", "author_id": "1461103969732751360", "author_username": "HHuntsmanWrites"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 186}}, {"conversation_id": "1915187244312523208", "tweet_id": "1915219704362525122", "timestamp": "2025-04-24T01:42:15+00:00", "timestamp_unix": 1745458935, "type": "comment", "text": "@CarlZha The U.S. agrees with decoupling with the communist", "context": {"type": "tweet", "id": "1915450599371649042", "text": "それはそうですよね。", "author_id": "154842627", "author_username": "yottan07jp"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 284}}, {"conversation_id": "1915761693223010794", "tweet_id": "1915807361782190576", "timestamp": "2025-04-25T16:37:23+00:00", "timestamp_unix": 1745599043, "type": "comment", "text": "@SpoxCHN_LinJian Yeah we know that 😂thanks", "context": {"type": "tweet", "id": "1918947762579677594", "text": "平常官方说话老严肃了，这次就一句“美方不要混淆视听”，搞得美国真就像个小孩子在胡闹似的", "author_id": "1857644352090288130", "author_username": "<PERSON>_<PERSON>_xing"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1178}}, {"conversation_id": "1917129172390187351", "tweet_id": "1917456322850873727", "timestamp": "2025-04-30T05:49:46+00:00", "timestamp_unix": 1745992186, "type": "comment", "text": "@Jingjing_Li Chicoms are forgotten", "context": {"type": "tweet", "id": "1918249288054018268", "text": "If the US decides to attack China I SOOO hope China will film it’s retaliation as it will be beautiful to watch! Hey I’d pay to see it!!!", "author_id": "60220394", "author_username": "silvio_pires"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1917415666577203716", "tweet_id": "1917456785159643447", "timestamp": "2025-04-30T05:51:36+00:00", "timestamp_unix": 1745992296, "type": "comment", "text": "@zhao_dashuai Still not getting it, you communist are hard headed…we don’t want you your junk or people, we are done with you, understand now…maybe", "context": {"type": "tweet", "id": "1917415666577203716", "text": "原始推文内容不可用", "author_id": "999061779870175232", "author_username": "<PERSON><PERSON>_dashuai"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 58}}, {"conversation_id": "1920573374100549727", "tweet_id": "1920638882191675681", "timestamp": "2025-05-09T00:36:07+00:00", "timestamp_unix": 1746750967, "type": "comment", "text": "@thecyrusjanssen No, Conservatives will not allow this to happen,no mercy for the Chicoms!", "context": {"type": "tweet", "id": "1920765097951023268", "text": "From “maximum pressure” to “managed retreat”—the tariff rollback underscores how economic realities trump rhetoric.", "author_id": "1252464973416030208", "author_username": "wangxh65"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 45}}, {"conversation_id": "1921849699629437077", "tweet_id": "1921912087409803406", "timestamp": "2025-05-12T12:55:23+00:00", "timestamp_unix": 1747054523, "type": "comment", "text": "@OopsGuess Not interested in this, full decoupling", "context": {"type": "tweet", "id": "1922316946818613455", "text": "That's good, but the BRICS cat is out of the bag, with the US and the EU seizing Russian financial asetts.", "author_id": "1622407953335681025", "author_username": "AdamFranque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 34}}, {"conversation_id": "1921891799884321186", "tweet_id": "1921912155235889383", "timestamp": "2025-05-12T12:55:39+00:00", "timestamp_unix": 1747054539, "type": "comment", "text": "@CarlZha America first", "context": {"type": "tweet", "id": "1922379540552204683", "text": "@atrupar Trump is weaker than ever, haha. Chinese people are calling <PERSON> a \"dog of <PERSON>\" and saying <PERSON> has helped China by protecting Pakistan, as its investments and trade routes were in danger\nhttps://t.co/YMEC1uN1EP", "author_id": "1879908966395731968", "author_username": "p<PERSON><PERSON><PERSON>son1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1921962639816905129", "tweet_id": "1922067048726659190", "timestamp": "2025-05-12T23:11:09+00:00", "timestamp_unix": 1747091469, "type": "comment", "text": "@OopsGuess Old news, move on!", "context": {"type": "tweet", "id": "1922160658642088178", "text": "@SnipingTrends @ces921 Read it and weep", "author_id": "1604377004924497921", "author_username": "straycat911911"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 155}}, {"conversation_id": "1921848148407857408", "tweet_id": "1922135328795545788", "timestamp": "2025-05-13T03:42:28+00:00", "timestamp_unix": 1747107748, "type": "comment", "text": "@Snofy8 English!", "context": {"type": "tweet", "id": "1922160373727117408", "text": "贏你媽，利益受損的都是人民，反而是現在買東西越來越貴，工資越來越少，最好就是沒有關稅（個人觀點）", "author_id": "1756922914291474432", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1922436207406985582", "tweet_id": "1922477663655969155", "timestamp": "2025-05-14T02:22:47+00:00", "timestamp_unix": 1747189367, "type": "comment", "text": "@Snofy8 English", "context": {"type": "tweet", "id": "1922698651304067553", "text": "👍👍", "author_id": "1323251330698211329", "author_username": "LiYifei18576934"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 214}}]}