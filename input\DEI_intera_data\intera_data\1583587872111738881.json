{"user_id": "1583587872111738881", "interactions_count": 47, "interactions": [{"conversation_id": "1921157920110117136", "tweet_id": "1921205058349273274", "timestamp": "2025-05-10T14:05:54+00:00", "timestamp_unix": 1746885954, "type": "comment", "text": "@fairydellaluna Fai un'opera di carità cristiana (dato il momento) per le \"zitelle\" come me e condividi!😅😜", "context": {"type": "tweet", "id": "1921157920110117136", "text": "原始推文内容不可用", "author_id": "1544610278612025344", "author_username": "<PERSON><PERSON>al<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 8, "quote_count": 0, "view_count": 400}}, {"conversation_id": "1921157920110117136", "tweet_id": "1921214749078937983", "timestamp": "2025-05-10T14:44:25+00:00", "timestamp_unix": 1746888265, "type": "comment", "text": "@fairydellaluna 😬😬😬 https://t.co/0VzmnKqfR2", "context": {"type": "tweet", "id": "1921214130259726841", "text": "@smilingPamPam 🤣🤣🤣🤣 <PERSON><PERSON><PERSON><PERSON>!", "author_id": "1544610278612025344", "author_username": "<PERSON><PERSON>al<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 208}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921253024908837317", "timestamp": "2025-05-10T17:16:30+00:00", "timestamp_unix": 1746897390, "type": "comment", "text": "@Massi1926 <PERSON><PERSON><PERSON>, ce vo 'o lanciafiamme!😅😂", "context": {"type": "tweet", "id": "1921252887667110155", "text": "@smilingPamPam Glassex a volontà 🤣🤣🤣", "author_id": "1308083022621794306", "author_username": "Massi1926"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 189}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921253801748492360", "timestamp": "2025-05-10T17:19:36+00:00", "timestamp_unix": 1746897576, "type": "comment", "text": "@Massi1926 <PERSON><PERSON><PERSON>, altrimenti ti veniva un conato!😅", "context": {"type": "tweet", "id": "1921253581027479619", "text": "@smilingPamPam Me sfuggit 🤔", "author_id": "1308083022621794306", "author_username": "Massi1926"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921256416309813392", "timestamp": "2025-05-10T17:29:59+00:00", "timestamp_unix": 1746898199, "type": "comment", "text": "@DiamondBlack10K <PERSON><PERSON>ù, hanno i cadaveri!🤦‍♀️😅", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 273}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921257894915309872", "timestamp": "2025-05-10T17:35:52+00:00", "timestamp_unix": 1746898552, "type": "comment", "text": "@DiamondBlack10K Io mi faccio la fisima pure se ho il copriletto con una pieghetta!😅😂", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 62}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921258073760362572", "timestamp": "2025-05-10T17:36:34+00:00", "timestamp_unix": 1746898594, "type": "comment", "text": "@Scorpio27023158 <PERSON><PERSON><PERSON>, ma che hanno? C'è bisogno del lanciafiamme!😅😂", "context": {"type": "tweet", "id": "1921257431008506174", "text": "@smilingPamPam Ad<PERSON>! 😂😂😂😂", "author_id": "1415672046571245579", "author_username": "Scorpio27023158"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 379}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921259423831339506", "timestamp": "2025-05-10T17:41:56+00:00", "timestamp_unix": 1746898916, "type": "comment", "text": "@Scorpio27023158 Mamma mia! Eh gi<PERSON>, poi devono fare le foto per i loro amichetti!😂😅", "context": {"type": "tweet", "id": "1921258453034525009", "text": "@smilingPamPam Non hanno tempo, Pam... devono riconteggiare i followerZ 😂😂😂", "author_id": "1415672046571245579", "author_username": "Scorpio27023158"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 85}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921259546019758515", "timestamp": "2025-05-10T17:42:25+00:00", "timestamp_unix": 1746898945, "type": "comment", "text": "@DiamondBlack10K 😂😅", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 50}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921263049027019256", "timestamp": "2025-05-10T17:56:20+00:00", "timestamp_unix": 1746899780, "type": "comment", "text": "@<PERSON>ris_quella Mamma mia Cris, dire schifo è poco!😅", "context": {"type": "tweet", "id": "1921259856117284975", "text": "@smilingPamPam Standing ovation 😂", "author_id": "1272570747039428609", "author_username": "<PERSON><PERSON>_quella"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 243}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921263229109412078", "timestamp": "2025-05-10T17:57:03+00:00", "timestamp_unix": 1746899823, "type": "comment", "text": "@tizianoghidoni A me niente!😅", "context": {"type": "tweet", "id": "1921260739488068078", "text": "@smilingPamPam <PERSON>e succede Pamela...?", "author_id": "450069214", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 149}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921263371921313812", "timestamp": "2025-05-10T17:57:37+00:00", "timestamp_unix": 1746899857, "type": "comment", "text": "@Scorpio27023158 😅😂 https://t.co/sktHNycjR7", "context": {"type": "tweet", "id": "1921261737245872281", "text": "@smilingPamPam 🤦‍♀️😂", "author_id": "1415672046571245579", "author_username": "Scorpio27023158"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 184}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921264500545589322", "timestamp": "2025-05-10T18:02:06+00:00", "timestamp_unix": 1746900126, "type": "comment", "text": "@ungiornopercas1 🤢", "context": {"type": "tweet", "id": "1921263987003433299", "text": "@smilingPamPam Temo che il loro pubblico non guardi lo sfondo", "author_id": "1387800812529782786", "author_username": "ungiornopercas1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 384}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921264594762268843", "timestamp": "2025-05-10T18:02:29+00:00", "timestamp_unix": 1746900149, "type": "comment", "text": "@IlBarone_Siculo Tu fai tutto all'aria aperta!😂😂😂", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 241}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921266448736846026", "timestamp": "2025-05-10T18:09:51+00:00", "timestamp_unix": 1746900591, "type": "comment", "text": "@me_deae <PERSON>aa?😬😬😬", "context": {"type": "tweet", "id": "1921265699877437613", "text": "@smilingPamPam <PERSON>aam 😂😂😂😂🤣🤣🤣🤣🤣🤣🤣🤣🤣", "author_id": "556181395", "author_username": "me_deae"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 326}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921267846602018923", "timestamp": "2025-05-10T18:15:24+00:00", "timestamp_unix": 1746900924, "type": "comment", "text": "@_sy_h2_sfn <PERSON>, mancano solo le larve di mosca😅🤢", "context": {"type": "tweet", "id": "1921261626767896866", "text": "@smilingPamPam 😅😅😅 ma sono dettagli", "author_id": "1719704467262586880", "author_username": "_sy_h2_sfn"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 367}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921267921017278768", "timestamp": "2025-05-10T18:15:42+00:00", "timestamp_unix": 1746900942, "type": "comment", "text": "@VuotoPerfetto 😅🤢🤦‍♀️", "context": {"type": "tweet", "id": "1921257735745618173", "text": "@smilingPamPam https://t.co/pHe4QPX9Xj", "author_id": "1900255436198285312", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 231}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921271287244075399", "timestamp": "2025-05-10T18:29:05+00:00", "timestamp_unix": 1746901745, "type": "comment", "text": "@deborapaola74 Debby non voglio nemmeno immaginare!🤢🤦‍♀️", "context": {"type": "tweet", "id": "1921269714417860704", "text": "@smilingPamPam 🤢🤢🤢\n<PERSON>oooo certi bagni fanno paura...🙈", "author_id": "872534716376907777", "author_username": "deborapaola74"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 422}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921271872085254434", "timestamp": "2025-05-10T18:31:24+00:00", "timestamp_unix": 1746901884, "type": "comment", "text": "@deborapaola74 È più pulito un bagno dell'autogrill... e ho detto tutto!😅😂", "context": {"type": "tweet", "id": "1921271458438771146", "text": "@smilingPamPam Ecco 🤣 meglio", "author_id": "872534716376907777", "author_username": "deborapaola74"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 43}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921273294583468516", "timestamp": "2025-05-10T18:37:03+00:00", "timestamp_unix": 1746902223, "type": "comment", "text": "@deborapaola74 😅😂", "context": {"type": "tweet", "id": "1921272216676614219", "text": "@smilingPamPam Esatto 🙈🤣", "author_id": "872534716376907777", "author_username": "deborapaola74"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 34}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921273766446850099", "timestamp": "2025-05-10T18:38:56+00:00", "timestamp_unix": 1746902336, "type": "comment", "text": "@BP_IlRitorno Preferisco essere con un sistema immunitario scarso allora!🤢", "context": {"type": "tweet", "id": "1921270341705330972", "text": "@smilingPamPam Vivere in ambienti puliti non aiuta a rinforzare il sistema immunitario.", "author_id": "1049741040029184001", "author_username": "BP_IlRitorno"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 5, "quote_count": 0, "view_count": 311}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921273956201288028", "timestamp": "2025-05-10T18:39:41+00:00", "timestamp_unix": 1746902381, "type": "comment", "text": "@Antonuzzo2 Ma è diverso, tutti ci sporchiamo lavorando, m poi ci laviamo... almeno si spera!😅😂", "context": {"type": "tweet", "id": "1921268325092303114", "text": "@smilingPamPam Be io sono sporco di lavoro nei campi 😅😂👋", "author_id": "1232319168483033088", "author_username": "Antonuzzo2"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 214}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921280517854335275", "timestamp": "2025-05-10T19:05:45+00:00", "timestamp_unix": 1746903945, "type": "comment", "text": "@Don4_B4ss L'alone è plausibile, ma non le dita di polvere, quello significa che proprio non pulisci!😅😂", "context": {"type": "tweet", "id": "1921275623000899832", "text": "@smilingPamPam E vogliamo parlare degli specchi? E non solo", "author_id": "880883773671387140", "author_username": "Don4_B4ss"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 266}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921280689212657737", "timestamp": "2025-05-10T19:06:26+00:00", "timestamp_unix": 1746903986, "type": "comment", "text": "@neverajoy_mya <PERSON><PERSON>, non sanno che significa usare uno strofinaccio o spugna!😅", "context": {"type": "tweet", "id": "1921277568642113924", "text": "@smilingPamPam 🤣", "author_id": "1306726033715073024", "author_username": "<PERSON><PERSON>oy_mya"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 249}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921280904816644139", "timestamp": "2025-05-10T19:07:18+00:00", "timestamp_unix": 1746904038, "type": "comment", "text": "@IlBarone_Siculo Pure qui stamattina pioveva!😮‍💨", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921280945702777032", "timestamp": "2025-05-10T19:07:27+00:00", "timestamp_unix": 1746904047, "type": "comment", "text": "@JeanGab63523069 😅😮‍💨", "context": {"type": "tweet", "id": "1921278631755559222", "text": "@smilingPamPam 😏😅😅😅", "author_id": "1467497574353575937", "author_username": "JeanGab63523069"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 103}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921283254503575695", "timestamp": "2025-05-10T19:16:38+00:00", "timestamp_unix": 1746904598, "type": "comment", "text": "@neverajoy_mya 🤦‍♀️😮‍💨😂", "context": {"type": "tweet", "id": "1921281358493614529", "text": "@smilingPamPam Ma che schifo 😂", "author_id": "1306726033715073024", "author_username": "<PERSON><PERSON>oy_mya"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921283448871829714", "timestamp": "2025-05-10T19:17:24+00:00", "timestamp_unix": 1746904644, "type": "comment", "text": "@Don4_B4ss 😂😅", "context": {"type": "tweet", "id": "1921281746147926432", "text": "@smilingPamPam L'alone sarebbe il meno 🤦🏻‍♂️🤦🏻‍♂️😂😂", "author_id": "880883773671387140", "author_username": "Don4_B4ss"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 37}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921283499081830803", "timestamp": "2025-05-10T19:17:36+00:00", "timestamp_unix": 1746904656, "type": "comment", "text": "@IlBarone_Siculo 😂😂😂", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921284911207465081", "timestamp": "2025-05-10T19:23:13+00:00", "timestamp_unix": 1746904993, "type": "comment", "text": "@IlBarone_Siculo Ioooo? E che ho fatto?🥺", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921287278753693839", "timestamp": "2025-05-10T19:32:37+00:00", "timestamp_unix": 1746905557, "type": "comment", "text": "@Piis20331773 😂😂😂", "context": {"type": "tweet", "id": "1921285248731463929", "text": "@smilingPamPam 😂😂😂😂🚽🚽🚽🚽", "author_id": "1260953597963051010", "author_username": "Piis20331773"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 145}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921287349662613805", "timestamp": "2025-05-10T19:32:54+00:00", "timestamp_unix": 1746905574, "type": "comment", "text": "@IlBarone_Siculo Nooo!🥺🥺😅", "context": {"type": "tweet", "id": "1921252637216788484", "text": "原始推文内容不可用", "author_id": "1583587872111738881", "author_username": "smilingPamPam"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 28}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921291400865325342", "timestamp": "2025-05-10T19:49:00+00:00", "timestamp_unix": 1746906540, "type": "comment", "text": "@il_nordico 😅", "context": {"type": "tweet", "id": "1921290565410238855", "text": "@smilingPamPam 😂😂😂😂", "author_id": "1506381068206809089", "author_username": "il_nordico"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 136}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921297712974070193", "timestamp": "2025-05-10T20:14:05+00:00", "timestamp_unix": 1746908045, "type": "comment", "text": "@vagar_mente 😂😂😂", "context": {"type": "tweet", "id": "1921297164514238686", "text": "@smilingPamPam 😳😳", "author_id": "931307891881062400", "author_username": "vagar_mente"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 190}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921298659016126739", "timestamp": "2025-05-10T20:17:50+00:00", "timestamp_unix": 1746908270, "type": "comment", "text": "@ChemicalAsdfg Chem, ci vorrebbe la soluzione piranha per quelle case!😅😂", "context": {"type": "tweet", "id": "1921297879777292305", "text": "@smiling<PERSON><PERSON><PERSON><PERSON>", "author_id": "3343884585", "author_username": "ChemicalAsdfg"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 149}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921299007852146900", "timestamp": "2025-05-10T20:19:14+00:00", "timestamp_unix": 1746908354, "type": "comment", "text": "@ChemicalAsdfg 😅😂", "context": {"type": "tweet", "id": "1921298800750055446", "text": "@smiling<PERSON><PERSON><PERSON><PERSON>", "author_id": "3343884585", "author_username": "ChemicalAsdfg"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921309687795921343", "timestamp": "2025-05-10T21:01:40+00:00", "timestamp_unix": 1746910900, "type": "comment", "text": "@Confession92971 È un termine molto eloquente!😅😂", "context": {"type": "tweet", "id": "1921307665357054107", "text": "@smilingPamPam Lerciume😅😅😅", "author_id": "1692494395000365056", "author_username": "Confession92971"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 138}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921335465845690769", "timestamp": "2025-05-10T22:44:06+00:00", "timestamp_unix": 1746917046, "type": "comment", "text": "@Figaro34604322 <PERSON><PERSON>, una constatazione!🤗", "context": {"type": "tweet", "id": "1921333727877066910", "text": "@smilingPamPam Che è successo?", "author_id": "1067814417453195264", "author_username": "Figaro34604322"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 123}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921335613195784410", "timestamp": "2025-05-10T22:44:41+00:00", "timestamp_unix": 1746917081, "type": "comment", "text": "@Vane_Sardegna Vane ci vuole solo una bomba!😅😂", "context": {"type": "tweet", "id": "1921334018571694380", "text": "@smilingPamPam Che schifo Pam! Ho visto foto con dello sporco incrostato che manco Mastro Lindo potrebbe fare qualcosa in merito! Puuuuuu", "author_id": "1348351520203558912", "author_username": "<PERSON><PERSON>_<PERSON>gna"}, "metrics": {"retweet_count": 1, "reply_count": 1, "like_count": 1, "quote_count": 1, "view_count": 381}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921338409546658019", "timestamp": "2025-05-10T22:55:48+00:00", "timestamp_unix": 1746917748, "type": "comment", "text": "@Figaro34604322 Grazie mille per la premura!🤗", "context": {"type": "tweet", "id": "1921335907942093160", "text": "@smilingPamPam Non farmi preoccupare", "author_id": "1067814417453195264", "author_username": "Figaro34604322"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 44}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921338615700889635", "timestamp": "2025-05-10T22:56:37+00:00", "timestamp_unix": 1746917797, "type": "comment", "text": "@Vane_Sardegna Stanno facendo gli shooting fotografici perciò non hanno tempo!😅😂", "context": {"type": "tweet", "id": "1921336122271010999", "text": "@smilingPamPam Si unica soluzione 😂\nE comunque quanto una persona è sporca si vede anche da queste cose. Che schifo.", "author_id": "1348351520203558912", "author_username": "<PERSON><PERSON>_<PERSON>gna"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 218}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921464662220005581", "timestamp": "2025-05-11T07:17:29+00:00", "timestamp_unix": 1746947849, "type": "comment", "text": "@_sy_h2_sfn 😅😂", "context": {"type": "tweet", "id": "1921284564925714931", "text": "@smilingPamPam https://t.co/HEf99muiT8", "author_id": "1719704467262586880", "author_username": "_sy_h2_sfn"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1921157920110117136", "tweet_id": "1921475684603498525", "timestamp": "2025-05-11T08:01:17+00:00", "timestamp_unix": 1746950477, "type": "comment", "text": "@nditta14 @fairydellaluna 🤭🤭🤭😅😂", "context": {"type": "tweet", "id": "1921474057502937463", "text": "@smilingPamPam @fairydellaluna 🤣🤣🤣sei la peggio!!!", "author_id": "1327694853753499649", "author_username": "nditta14"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921602553281757422", "timestamp": "2025-05-11T16:25:25+00:00", "timestamp_unix": 1746980725, "type": "comment", "text": "@nditta14 <PERSON><PERSON>, MastroLindo si è dato alla fuga quando ha visto!😅😅", "context": {"type": "tweet", "id": "1921601994021655026", "text": "@smilingPamPam questo mastro lindo?! https://t.co/V4rPHUBE9I", "author_id": "1327694853753499649", "author_username": "nditta14"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 48}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921637887730221124", "timestamp": "2025-05-11T18:45:49+00:00", "timestamp_unix": 1746989149, "type": "comment", "text": "@kingofthe<PERSON> Tutte e 2 chi? Io ho fatto una constatazione!😅", "context": {"type": "tweet", "id": "1921633828617040151", "text": "@smilingPamPam Certo che fra tutte due, se doveste mungere una mucca 🐮, ne uscirebbe direttamente yogurt! 😉 😂 🤣", "author_id": "81462019", "author_username": "king<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 60}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921668973633929321", "timestamp": "2025-05-11T20:49:20+00:00", "timestamp_unix": 1746996560, "type": "comment", "text": "@kingoftheheart Basta avere gli occhi!🤗", "context": {"type": "tweet", "id": "1921668658637508961", "text": "@smilingPamPam (poco) amichevole!  😂 🤣 https://t.co/8bya5xOhAV", "author_id": "81462019", "author_username": "king<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 42}}, {"conversation_id": "1921252637216788484", "tweet_id": "1921680491847000330", "timestamp": "2025-05-11T21:35:07+00:00", "timestamp_unix": 1746999307, "type": "comment", "text": "@Stefanialove_of Ciò che è fuori è introspettivo di ciò che è dentro!😌", "context": {"type": "tweet", "id": "1921676449263518033", "text": "@smilingPamPam A me preoccupa più lo sporco che hanno dentro", "author_id": "3106260993", "author_username": "Stefanialove_of"}, "metrics": {"retweet_count": 2, "reply_count": 3, "like_count": 2, "quote_count": 1, "view_count": 250}}]}