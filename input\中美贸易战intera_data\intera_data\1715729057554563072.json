{"user_id": "1715729057554563072", "interactions_count": 22, "interactions": [{"conversation_id": "1886647192183882207", "tweet_id": "1886703052066828316", "timestamp": "2025-02-04T09:07:15+00:00", "timestamp_unix": 1738660035, "type": "comment", "text": "@bbcchinese 打架，这方面我比较喜欢", "context": {"type": "tweet", "id": "1887013510431035529", "text": "比想象中友好。和平万岁。", "author_id": "1240065766528929794", "author_username": "as4794727"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 181}}, {"conversation_id": "1909931733216121149", "tweet_id": "1909940312015089712", "timestamp": "2025-04-09T12:03:50+00:00", "timestamp_unix": 1744200230, "type": "comment", "text": "@GlobeEyeNews 能被替代的产业链，在第一次贸易战中，已经替代了！", "context": {"type": "tweet", "id": "1910524397854589094", "text": "Cool, don’t care, I’ve long held the idea we need to stop being China’s bitch and do for ourselves\n\nAllowing manufacturing to leave the US and go to China in favor of “muh slave labor makes it cheaper” has been more economically damaging than Trump’s tariffs https://t.co/K1TqwXRrbb", "author_id": "1393322376100724739", "author_username": "SlimeFrosted"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1912092756945576197", "tweet_id": "1912463845160861896", "timestamp": "2025-04-16T11:11:27+00:00", "timestamp_unix": 1744801887, "type": "comment", "text": "@MarioNawfal 中国商飞的供应链已经可以去除美国产品了", "context": {"type": "tweet", "id": "1913821070097870914", "text": "🇺🇸 BOEING JET RETURNS HOME AFTER CHINA SAYS “NO THANKS” TO TRUMP’S TARIFFS\n\nA shiny new 737 meant for China just flew back to Seattle like a rejected prom date—tariffs made the sale way too spicy.\n\nThe plane, fully painted for Xiamen Airlines, took the long way home after China hit U.S. goods with a 125% tariff in response to Trump’s 145% import hike.\n\nWith price tags pushing $55 million, airlines are ghosting deliveries rather than coughing up the cash.\n\nSource: Reuters", "author_id": "1319287761048723458", "author_username": "MarioNawfal"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1914350606153634238", "tweet_id": "1914464996614136264", "timestamp": "2025-04-21T23:43:18+00:00", "timestamp_unix": 1745278998, "type": "comment", "text": "@KotrikePrem @BRICSinfo 不，只有美国完了！世界会更美好", "context": {"type": "tweet", "id": "1914353238331343097", "text": "@BRICSinfo Imagine China being the superpower of the world. If USA doesn’t make them bend the knee, the world is cooked. Very few understand this, while the rest complain about trade war and stock market being down.", "author_id": "1209154223201325056", "author_username": "KotrikePrem"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1914748539651150075", "tweet_id": "1914884811979936106", "timestamp": "2025-04-23T03:31:30+00:00", "timestamp_unix": 1745379090, "type": "comment", "text": "@TGTM_Official 很正常，韩国54%，日本76%，欧洲34%，都是中国制造", "context": {"type": "tweet", "id": "1916867442913186296", "text": "Problems in the post tariff world", "author_id": "981726531570552832", "author_username": "aksh_2018"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1914715435183780158", "tweet_id": "1914934256964379127", "timestamp": "2025-04-23T06:47:59+00:00", "timestamp_unix": 1745390879, "type": "comment", "text": "@Thiessen90Xx @BRICSinfo 那就继续打贸易战，千万别跪下来求饶！", "context": {"type": "tweet", "id": "1914823919254823190", "text": "China is aggressively threatening countries that are renegotiating trade deals with the United States, aiming to deter them from aligning with Washington’s economic strategies. These threats are a direct reaction to the U.S. pushing bilateral trade agreements that hurt China’s export-driven economy. Specifically, the U.S. is crafting deals that stop countries from letting Chinese goods be relabeled or transshipped through their territories to avoid American tariffs—a practice China has relied on to keep its exports flowing. By issuing stark warnings of retaliation, China hopes to scare these nations into staying loyal to its trade interests.\n\nHowever, this approach is backfiring. Instead of intimidating countries into compliance, China’s heavy-handed tactics are driving them toward the U.S. For example, Vietnam recently agreed to buy 26 U.S. F-16 fighter jets after a visit from Chinese leader <PERSON>, a clear sign that China’s pressure is pushing nations away rather than pulling them closer. China’s bullying only reinforces the U.S. argument that Beijing is an unreliable and coercive partner, making the U.S. look like a better option by comparison. Far from protecting its trade dominance, China’s threats are speeding up its own isolation, proving that its strategy is hurting itself more than it’s hurting its rivals.", "author_id": "1824423754619080704", "author_username": "Thiessen90Xx"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1915320507840208904", "tweet_id": "1915342785647202712", "timestamp": "2025-04-24T09:51:20+00:00", "timestamp_unix": 1745488280, "type": "comment", "text": "@lufan666c @wangzhian8848 没信用的东西，给脸不要脸而已", "context": {"type": "tweet", "id": "1915330427793084867", "text": "@wangzhian8848 这个我也是一直没搞懂的，特朗普早在上任前就多次对中国释放好感，上任初就表明要100天内访问中国，关税加征开始特朗普也多次释放谈判意愿，为什么这边感觉完全不给台阶下异常强硬，甚至比拜登还强硬。", "author_id": "1599750695833415685", "author_username": "lufan666c"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 79}}, {"conversation_id": "1915744721097412857", "tweet_id": "1915763014197973323", "timestamp": "2025-04-25T13:41:10+00:00", "timestamp_unix": 1745588470, "type": "comment", "text": "@PatriotRose17 @WatcherGuru 谁是美国代表？谁是中国代表？在哪个城市会谈？谈了什么？请说明！最后告诉你25号习近平整体在开会，根本没给特朗普打电话！", "context": {"type": "tweet", "id": "1915755001760059467", "text": "@WatcherGuru They’re lying", "author_id": "1482548969012142082", "author_username": "PatriotRose17"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 123}}, {"conversation_id": "1916794010707935283", "tweet_id": "1916821913986568653", "timestamp": "2025-04-28T11:48:51+00:00", "timestamp_unix": 1745840931, "type": "comment", "text": "@Investingcom 在美国恢复诚信前，无论高层还是底层，中国已经放弃了任何谈判机会！", "context": {"type": "tweet", "id": "1916919822895493378", "text": "Bessent should be fired by @elonmusk immediately", "author_id": "1832040719805992960", "author_username": "themarketlocust"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1917129172390187351", "tweet_id": "1917425777488060644", "timestamp": "2025-04-30T03:48:24+00:00", "timestamp_unix": 1745984904, "type": "comment", "text": "@Manygoats_TX @AlexSad75177637 @Jingjing_Li 成为美国人？傻子才做的事，谁会去一个地狱国家，不停的纳税，不停的通货膨胀，不停的侵犯与伤害他国！", "context": {"type": "tweet", "id": "1917372064241181050", "text": "@AlexSad75177637 @Jingjing_Li Sounds like the 1% wins. So you want to be American🇺🇸 now? 🤔", "author_id": "1727454147119620096", "author_username": "Manygoats_TX"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1917250269508784134", "tweet_id": "1917543711782887668", "timestamp": "2025-04-30T11:37:01+00:00", "timestamp_unix": 1746013021, "type": "comment", "text": "@JoeySalads @nicksortor 那么你就没有货物可以购买🤣🤣", "context": {"type": "tweet", "id": "1919137210164519022", "text": "I don’t understand why folks wouldn’t want to see a breakdown of the cost of items.", "author_id": "623306535", "author_username": "LetsGoBucsHD56"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 0}}, {"conversation_id": "1917644167163044080", "tweet_id": "1917891483371475386", "timestamp": "2025-05-01T10:38:56+00:00", "timestamp_unix": 1746095936, "type": "comment", "text": "@WatcherGuru 胡说八道，没兴趣", "context": {"type": "tweet", "id": "1917980677309358440", "text": "What's that song that goes \"beggin, beggin you\"", "author_id": "986982171838730240", "author_username": "zackjp86"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 0}}, {"conversation_id": "1918393330125189618", "tweet_id": "1918479399424540915", "timestamp": "2025-05-03T01:35:07+00:00", "timestamp_unix": 1746236107, "type": "comment", "text": "@LimbaughLegacy @jaydenbango @Megatron_ron 所以宁可美国人冻死或饿死！也要爱一个不爱你的美国🤣🤣🤣", "context": {"type": "tweet", "id": "1918403723501457644", "text": "@jaydenbango @Megatron_ron Didn’t call them cheap. I called them unpatriotic.  Plus, 110 million Americans don’t use Temu.", "author_id": "1587473418429005827", "author_username": "LimbaughLegacy"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1919754346356498788", "tweet_id": "1919930315411279999", "timestamp": "2025-05-07T01:40:32+00:00", "timestamp_unix": 1746582032, "type": "comment", "text": "@PressSec 🤣🤣🤣🤣，作为中国人，你确实让我笑得停不下来", "context": {"type": "tweet", "id": "1922472005334434022", "text": "#Swadeshi\n\nSwadeshi policies of president @POTUS <PERSON> @realDonaldTrump ♥️\n.", "author_id": "1485172586028093440", "author_username": "Sv4599"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1920802887535652998", "tweet_id": "1920822291136438477", "timestamp": "2025-05-09T12:44:56+00:00", "timestamp_unix": 1746794696, "type": "comment", "text": "@trump_repost 🤣🤣🤣，继续看小丑表演！", "context": {"type": "tweet", "id": "1921309036751782090", "text": "@He<PERSON>_Han @CPhotographyC1 @EllaSunMMP https://t.co/zQqjs5jN5B 三等黄皮贱种就你这样的", "author_id": "1902688000909459456", "author_username": "Leesir1124"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 100}}, {"conversation_id": "1921007666488390017", "tweet_id": "1921064817193824675", "timestamp": "2025-05-10T04:48:38+00:00", "timestamp_unix": 1746852518, "type": "comment", "text": "@zaobaosg 啤酒，瓜子，板凳，坐看小丑表演", "context": {"type": "tweet", "id": "1921602815710871782", "text": "80%，和145%有啥区别，只要超过50%就是搞笑了 #tariffs", "author_id": "1847928988305391616", "author_username": "Physikmat<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 114}}, {"conversation_id": "1920808611124592938", "tweet_id": "1921125622698447107", "timestamp": "2025-05-10T08:50:15+00:00", "timestamp_unix": 1746867015, "type": "comment", "text": "@RonFilipkowski 🤣🤣🤣，板凳，瓜子，啤酒，我要看他继续表演！", "context": {"type": "tweet", "id": "1921930793472999501", "text": "No Libtard we just laugh at you. And this post aged poorly by the way.", "author_id": "532622381", "author_username": "hcmv007"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1920892480607703460", "tweet_id": "1921255276839079968", "timestamp": "2025-05-10T17:25:27+00:00", "timestamp_unix": 1746897927, "type": "comment", "text": "@FirstSquawk I'm Chinese. Right now, I need a stool, some beer and peanuts to watch the US government continue to put on a clown show.😂😂😂", "context": {"type": "tweet", "id": "1920897056618541448", "text": "Yeah. We know", "author_id": "2209608155", "author_username": "AurelianoPisa"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921630471127019598", "tweet_id": "1921801867291459595", "timestamp": "2025-05-12T05:37:25+00:00", "timestamp_unix": 1747028245, "type": "comment", "text": "@StratsLabs @rovercrc 中國放棄美元，就不會有逆差了🤣🤣🤣", "context": {"type": "tweet", "id": "1921925914675360010", "text": "If that's true it will snowball Planetwide within 6 months , this could really be the biggest news if the last 20", "author_id": "1882075205360893952", "author_username": "l1ju1n"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 106}}, {"conversation_id": "1921856789982617601", "tweet_id": "1921889153362358583", "timestamp": "2025-05-12T11:24:15+00:00", "timestamp_unix": 1747049055, "type": "comment", "text": "@dw_chinese 欧洲媒体酸酸的，说的好像2-3月份针对美国原油，天然气，农产品的订单与关税会回去似的🤣🤣", "context": {"type": "tweet", "id": "1922150063863644410", "text": "日本語訳🇯🇵 https://t.co/5uVbgS94I9", "author_id": "1858146376871530496", "author_username": "maitan4162"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 1013}}, {"conversation_id": "1922255503750779163", "tweet_id": "1922270603681182185", "timestamp": "2025-05-13T12:40:00+00:00", "timestamp_unix": 1747140000, "type": "comment", "text": "@0megalvl3vent @MarioNawfal 你没有听过自动化生产和机器人生产模式？🤣🤣", "context": {"type": "tweet", "id": "1922261642802295236", "text": "@MarioNawfal They mad but in order to shift power, the yuan must rise in value and if that happens, China will never be able to afford manufacturing in their country by rise in cost of living, they themselves have put on their shackles to the sweatshop.", "author_id": "1605591081889980420", "author_username": "0megalvl3vent"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1922103972120428661", "tweet_id": "1922320436815167673", "timestamp": "2025-05-13T15:58:01+00:00", "timestamp_unix": 1747151881, "type": "comment", "text": "@TheXiangYang 笑死我了🤣🤣，就你这水平也敢点评时政？", "context": {"type": "tweet", "id": "1922398362722025731", "text": "主要是官员的利益是家属移美。所以，他们不代表习的利益。", "author_id": "704519019601260545", "author_username": "JoyHua88"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 319}}]}