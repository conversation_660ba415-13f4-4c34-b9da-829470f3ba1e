{"user_id": "1823146814108794880", "interactions_count": 16, "interactions": [{"conversation_id": "1920152594501997003", "tweet_id": "1920316215227978169", "timestamp": "2025-05-08T03:13:58+00:00", "timestamp_unix": 1746674038, "type": "comment", "text": "@PeriklesGREAT Yes", "context": {"type": "tweet", "id": "1920531123601617404", "text": "Yes", "author_id": "786304953652383744", "author_username": "jht6_harold"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 0}}, {"conversation_id": "1920939251882946790", "tweet_id": "1921300511933788263", "timestamp": "2025-05-10T20:25:12+00:00", "timestamp_unix": 1746908712, "type": "comment", "text": "@PeriklesGREAT PTL!", "context": {"type": "tweet", "id": "1921358121873612862", "text": "https://t.co/W7eRlpDTSW\nEarth has 400ppm CO2, needs 1500.\nhttps://t.co/jflsuCDb4o\nFarmers greenhouses ADD CO2.\nChina 1100 COAL PLANTS now Build 900 NEW.\n1600 NEW Worldwide \nClimate Change is 100% Pure SCAM ! !\nAll to destroy the west !\nFAKE ! !", "author_id": "2322482252", "author_username": "h5x22"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 0}}, {"conversation_id": "1921956272607252790", "tweet_id": "1922069464972939378", "timestamp": "2025-05-12T23:20:45+00:00", "timestamp_unix": 1747092045, "type": "comment", "text": "@PeriklesGREAT Yes.", "context": {"type": "tweet", "id": "1922607870417268759", "text": "Yes", "author_id": "78071894", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 0}}, {"conversation_id": "1921721398118002696", "tweet_id": "1922084653512393028", "timestamp": "2025-05-13T00:21:06+00:00", "timestamp_unix": 1747095666, "type": "comment", "text": "@JDunlap1974 Inciting an insurrection?", "context": {"type": "tweet", "id": "1922815514650268095", "text": "@ChloeBrook63 @TRUSTGOD2020 @KatAMiller10 @MagaPhoenix73 @surgerysleeper @America1Eagle1 @scottstocker4 @Susan9577261605 @UndeC08 @wrongwaywilly56 @<PERSON><PERSON><PERSON><PERSON> @PPPTCE @FuxUP0s @kcinor @HansScottfmLI @clockwork584043 @MrUltraMarin @CatWomanVonni @S51781902 @traveler002 @momvogt1 @Quin4Trump @ICanPlainlySee @ernestleenot @Gigi69030829 @1GaryBernstein @KerryOLeary4 @Godslightangel7 @JeffWoke @sherry_fremont @Jeanetteisback @no2commiez @NanetteDonnelly @jazonuxlay @wildflowers1172 @perinejennifer5 @hrt6017 @tnolwene @freemasongeorg5 @DeplorableNew @1eyedwilly56 @acfueler @mccuan_lois She might …lol\n\nAnother thug. Who serves in Congress.", "author_id": "703631194601222145", "author_username": "nicher66"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1921948364964241906", "tweet_id": "1922085465584247012", "timestamp": "2025-05-13T00:24:20+00:00", "timestamp_unix": 1747095860, "type": "comment", "text": "@PeriklesGREAT All of the pardons.", "context": {"type": "tweet", "id": "1922287585025482854", "text": "Of course no need to even ask.  The pardons should me null and void", "author_id": "1007128218770558976", "author_username": "DonnaWa60577617"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 0}}, {"conversation_id": "1922063567655743697", "tweet_id": "1922144891246633180", "timestamp": "2025-05-13T04:20:28+00:00", "timestamp_unix": 1747110028, "type": "comment", "text": "@PeriklesGREAT Yes.", "context": {"type": "tweet", "id": "1922640567466414384", "text": "Agree!", "author_id": "1519458161035468800", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1922299773383520678", "tweet_id": "1922440547819520285", "timestamp": "2025-05-13T23:55:18+00:00", "timestamp_unix": 1747180518, "type": "comment", "text": "@PeriklesGREAT Yes.", "context": {"type": "tweet", "id": "1923021619024257065", "text": "https://t.co/VtRZUTK4ql", "author_id": "2562145560", "author_username": "foxxylilfoxxW"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1922271263696863305", "tweet_id": "1922456970528686491", "timestamp": "2025-05-14T01:00:33+00:00", "timestamp_unix": 1747184433, "type": "comment", "text": "@WHLeavitt Amen!", "context": {"type": "tweet", "id": "1922425441006088387", "text": "FAFO", "author_id": "1884372908476350464", "author_username": "BettyOaks26648"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1922734935900561555", "tweet_id": "1923064129998237905", "timestamp": "2025-05-15T17:13:12+00:00", "timestamp_unix": 1747329192, "type": "comment", "text": "@PeriklesGREAT Each and every one of them.", "context": {"type": "tweet", "id": "1923226759144583172", "text": "100%  Yes!!", "author_id": "1524590890202763266", "author_username": "SueHarr06472823"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 0}}, {"conversation_id": "1922708297347391635", "tweet_id": "1923067823678517468", "timestamp": "2025-05-15T17:27:52+00:00", "timestamp_unix": 1747330072, "type": "comment", "text": "@PeriklesGREAT The problem of TOO MUCH WINNING!", "context": {"type": "tweet", "id": "1922994711683379659", "text": "if they break the law arrest them THIS GOES FOR REPUBLICANS TOO!", "author_id": "1622562782062206977", "author_username": "rbrandt2911"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1922734935900561555", "tweet_id": "1923167967887429727", "timestamp": "2025-05-16T00:05:48+00:00", "timestamp_unix": 1747353948, "type": "comment", "text": "@PeriklesGREAT Yes.", "context": {"type": "tweet", "id": "1923226759144583172", "text": "100%  Yes!!", "author_id": "1524590890202763266", "author_username": "SueHarr06472823"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1923633361475817682", "tweet_id": "1923816470779195629", "timestamp": "2025-05-17T19:02:44+00:00", "timestamp_unix": 1747508564, "type": "comment", "text": "@WHLeavitt Yes.", "context": {"type": "tweet", "id": "1924098827230834914", "text": "AGREE", "author_id": "1858583060670779392", "author_username": "NevelDawn"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 1, "view_count": 5}}, {"conversation_id": "1924080639734681799", "tweet_id": "1924335696489127968", "timestamp": "2025-05-19T05:25:57+00:00", "timestamp_unix": 1747632357, "type": "comment", "text": "@1<PERSON><PERSON>dar Yes.", "context": {"type": "tweet", "id": "1924443072307376567", "text": "Yes.", "author_id": "1823074712001388546", "author_username": "Truethefacts77"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1924315934505996533", "tweet_id": "1924496772040188124", "timestamp": "2025-05-19T16:06:00+00:00", "timestamp_unix": 1747670760, "type": "comment", "text": "@<PERSON>ther<PERSON><PERSON><PERSON>an Amen.", "context": {"type": "tweet", "id": "1924315934505996533", "text": "原始推文内容不可用", "author_id": "1355721251180961792", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1924315934505996533", "tweet_id": "1924497514142588949", "timestamp": "2025-05-19T16:08:57+00:00", "timestamp_unix": 1747670937, "type": "comment", "text": "@GuntherEagleman And we'll even get rid of a female \"conservative \" justice to show good faith.\nDJT can then place true constitutional purists on the bench.", "context": {"type": "tweet", "id": "1924315934505996533", "text": "原始推文内容不可用", "author_id": "1355721251180961792", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1924187983722246186", "tweet_id": "1924539978106712172", "timestamp": "2025-05-19T18:57:41+00:00", "timestamp_unix": 1747681061, "type": "comment", "text": "@_NewsB<PERSON>ron And here I was thinking she was the Creature frin <PERSON>.", "context": {"type": "tweet", "id": "1924475161413124217", "text": "She's hilarious", "author_id": "1602333058585296896", "author_username": "jimmieje2018"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}]}