<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 25px;
        }
        .summary-box {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        .chart-container {
            text-align: center;
            margin: 30px 0;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .topic-section {
            margin: 40px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .metric-list {
            list-style-type: none;
            padding: 0;
        }
        .metric-list li {
            padding: 5px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .metric-list li:last-child {
            border-bottom: none;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>社交媒体数据分析报告</h1>
        
        <div class="summary-box">
            <h2>📊 数据概览</h2>
            <p>本报告分析了三个主要话题的社交媒体交互数据及其对应的意图分析结果。数据包含输入的原始社交媒体交互数据和输出的AI分析结果。</p>
        </div>

        <h2>🔢 关键统计数据</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">1,494</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">37,262</div>
                <div class="stat-label">总交互数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">273</div>
                <div class="stat-label">总分析数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">88.3%</div>
                <div class="stat-label">平均共识达成率</div>
            </div>
        </div>

        <h2>📈 输入数据分析</h2>
        
        <div class="chart-container">
            <h3>数据概览</h3>
            <img src="charts/input_overview.png" alt="输入数据概览">
        </div>

        <div class="chart-container">
            <h3>交互类型分布</h3>
            <img src="charts/interaction_types.png" alt="交互类型分布">
        </div>

        <div class="chart-container">
            <h3>用户交互数量分布</h3>
            <img src="charts/interactions_per_user.png" alt="用户交互数量分布">
        </div>

        <h3>社交媒体指标分布</h3>
        <div class="chart-container">
            <h4>点赞数分布</h4>
            <img src="charts/like_count_distribution.png" alt="点赞数分布">
        </div>

        <div class="chart-container">
            <h4>转发数分布</h4>
            <img src="charts/retweet_count_distribution.png" alt="转发数分布">
        </div>

        <div class="chart-container">
            <h4>回复数分布</h4>
            <img src="charts/reply_count_distribution.png" alt="回复数分布">
        </div>

        <div class="chart-container">
            <h4>浏览数分布</h4>
            <img src="charts/view_count_distribution.png" alt="浏览数分布">
        </div>

        <h2>📊 输出数据分析</h2>
        
        <div class="chart-container">
            <h3>分析结果概览</h3>
            <img src="charts/output_overview.png" alt="输出数据概览">
        </div>

        <div class="chart-container">
            <h3>意图分类分布</h3>
            <img src="charts/intent_categories.png" alt="意图分类分布">
        </div>

        <div class="chart-container">
            <h3>情感倾向分布</h3>
            <img src="charts/emotional_tendencies.png" alt="情感倾向分布">
        </div>

        <div class="chart-container">
            <h3>置信度分布</h3>
            <img src="charts/confidence_scores.png" alt="置信度分布">
        </div>

        <div class="chart-container">
            <h3>辩论轮数分布</h3>
            <img src="charts/debate_rounds.png" alt="辩论轮数分布">
        </div>

        <div class="chart-container">
            <h3>评估分数分布</h3>
            <img src="charts/evaluation_scores.png" alt="评估分数分布">
        </div>

        <h2>📋 详细统计</h2>
        
        <div class="topic-section">
            <h3>🏷️ DEI话题</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">494</div>
                    <div class="stat-label">用户数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">12,759</div>
                    <div class="stat-label">总交互数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25.8</div>
                    <div class="stat-label">平均每用户交互数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">78.4%</div>
                    <div class="stat-label">共识达成率</div>
                </div>
            </div>
            <div class="highlight">
                <strong>主要特征：</strong>DEI话题主要以评论(comment)为主(98.9%)，情感倾向以负面为主(71.1%)，意图分类以抵抗型(resistant)为主导。
            </div>
        </div>

        <div class="topic-section">
            <h3>🌏 中美贸易战话题</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">500</div>
                    <div class="stat-label">用户数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">14,773</div>
                    <div class="stat-label">总交互数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">29.5</div>
                    <div class="stat-label">平均每用户交互数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">87.7%</div>
                    <div class="stat-label">共识达成率</div>
                </div>
            </div>
            <div class="highlight">
                <strong>主要特征：</strong>中美贸易战话题交互最为活跃，平均浏览数最高(1001.8)，情感倾向极度负面(91.4%)，意图分类较为多样化。
            </div>
        </div>

        <div class="topic-section">
            <h3>💰 美联储降息话题</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">500</div>
                    <div class="stat-label">用户数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">9,730</div>
                    <div class="stat-label">总交互数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">19.5</div>
                    <div class="stat-label">平均每用户交互数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98.9%</div>
                    <div class="stat-label">共识达成率</div>
                </div>
            </div>
            <div class="highlight">
                <strong>主要特征：</strong>美联储降息话题共识达成率最高，包含更多原创推文(tweet)，意图分类相对集中在抵抗型、主动型和观察型。
            </div>
        </div>

        <h2>🎯 关键发现</h2>
        <div class="summary-box">
            <ul>
                <li><strong>交互模式：</strong>所有话题都以评论(comment)为主要交互方式，占比超过95%</li>
                <li><strong>情感倾向：</strong>三个话题都呈现负面情感主导，反映了争议性话题的特征</li>
                <li><strong>参与度差异：</strong>中美贸易战话题参与度最高，美联储降息话题相对较低</li>
                <li><strong>分析质量：</strong>AI分析的平均置信度较高(0.925)，共识达成率良好(88.3%)</li>
                <li><strong>意图复杂性：</strong>不同话题展现出不同的意图分类模式，反映了话题特性的差异</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #7f8c8d;">
            <p>报告生成时间: <script>document.write(new Date().toLocaleString('zh-CN'))</script></p>
        </div>
    </div>
</body>
</html>
