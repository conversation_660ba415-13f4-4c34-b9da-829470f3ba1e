{"user_id": "4796337323", "interactions_count": 12, "interactions": [{"conversation_id": "1920375120448672215", "tweet_id": "1920539585748099079", "timestamp": "2025-05-08T18:01:33+00:00", "timestamp_unix": 1746727293, "type": "comment", "text": "@M_Rachele Ma dirà il cazzo che vuole o deve venire a chiedere un parere a te?", "context": {"type": "tweet", "id": "1921658088492249170", "text": "<PERSON><PERSON> non mi rappresenta un paese dove uno dei suoi più famosi interpreti canori è frocio", "author_id": "1636757911153123328", "author_username": "juspotestas"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 40}}, {"conversation_id": "1920369530108313848", "tweet_id": "1920897692839928294", "timestamp": "2025-05-09T17:44:33+00:00", "timestamp_unix": 1746812673, "type": "comment", "text": "@ImmaS66162 Per<PERSON><PERSON> magari la prossima volta usa almeno la comprensione di quello che scrivi. Rincoglionita. https://t.co/TJeKE0SMwv", "context": {"type": "tweet", "id": "1920805045244715185", "text": "La colpa dei sinistri è stata quella di non avere appeso tutti i destri ai tempi… questa è la verità", "author_id": "1016280289239330818", "author_username": "<PERSON>_the_one_"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1920836723795218915", "tweet_id": "1920937370251038975", "timestamp": "2025-05-09T20:22:13+00:00", "timestamp_unix": 1746822133, "type": "comment", "text": "@DanieleGiova50 Ma la laurea in medicina l’hai trovata nelle patatine tu?", "context": {"type": "tweet", "id": "1921498843939590329", "text": "Uno che non trova la ma china dove l’ha parcheggiata e’ meglio che non si abbandoni in analisi sgangherate", "author_id": "1886013391149715456", "author_username": "Bati04545472991"}, "metrics": {"retweet_count": 0, "reply_count": 13, "like_count": 17, "quote_count": 0, "view_count": 928}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921144109562986863", "timestamp": "2025-05-10T10:03:43+00:00", "timestamp_unix": 1746871423, "type": "comment", "text": "@MarinaDeMattei @DanieleGiova50 Per fortuna ce l’hai con la sua infinite intelligenza, signora.", "context": {"type": "tweet", "id": "1920976951281426495", "text": "@iPat_Pd @DanieleGiova50 Beata. IGNORAnza...", "author_id": "342839599", "author_username": "MarinaDeMattei"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921144263301066895", "timestamp": "2025-05-10T10:04:20+00:00", "timestamp_unix": 1746871460, "type": "comment", "text": "@Vi25653<PERSON><PERSON><PERSON><PERSON> @DanieleGiova50 Così Disse Annalis<PERSON>, nota scienziata e premio Nobel.", "context": {"type": "tweet", "id": "1921131977928839629", "text": "@iPat_Pd @DanieleGiova50 E tu sicuramente non hai passato l esame di terza media", "author_id": "1735748268489842688", "author_username": "Vi2565<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 58}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921144718269841532", "timestamp": "2025-05-10T10:06:08+00:00", "timestamp_unix": 1746871568, "type": "comment", "text": "@jeanpaulvgp @DanieleGiova50 Meno male c’è lei che ha la faccia sveglia e se n’è accorto.", "context": {"type": "tweet", "id": "1921498843939590329", "text": "Uno che non trova la ma china dove l’ha parcheggiata e’ meglio che non si abbandoni in analisi sgangherate", "author_id": "1886013391149715456", "author_username": "Bati04545472991"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921233873100214521", "timestamp": "2025-05-10T16:00:24+00:00", "timestamp_unix": 1746892824, "type": "comment", "text": "@MAlex1962 @DanieleGiova50 Ah ok. <PERSON><PERSON> molto: C’era mia nonna che se avesse avuto le ruote sarebbe stata una carriola.\n\nHa anche qualcosa di intelligente da aggiungere ora?", "context": {"type": "tweet", "id": "1921210448512196647", "text": "@iPat_Pd @DanieleGiova50 C'era un premio nobel della medicina che dopo aver parlato contro i vaccini è stato trattato come una merda ora si muore così e non si sa nulla però si sa cosa ha mangiato e cagato una mummia 4000 anni fa. Continuate", "author_id": "3080289052", "author_username": "MAlex1962"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921234007225708722", "timestamp": "2025-05-10T16:00:56+00:00", "timestamp_unix": 1746892856, "type": "comment", "text": "@Dobrosan67 @DanieleGiova50 Mi basta vedere la sua faccia particolarmente sveglia per consolarmi molto.", "context": {"type": "tweet", "id": "1921174277430084050", "text": "@iPat_Pd @DanieleGiova50 Ti sei misurato qualche volta,il tuo quoziente d'intelligenza?", "author_id": "1769486063913914368", "author_username": "Dobrosan67"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921526469378912522", "timestamp": "2025-05-11T11:23:05+00:00", "timestamp_unix": 1746962585, "type": "comment", "text": "@Serge48253707 <PERSON><PERSON>i che dalla sua faccia sveglia si capisce tutto. <PERSON><PERSON> seghe davanti alle foto dei bonazzi per carità.", "context": {"type": "tweet", "id": "1921266975013019728", "text": "@iPat_Pd @DanieleGiova50 Immagino che tu l' abbia trovata nei pop corn☺️☺️☺️", "author_id": "1399077226671452163", "author_username": "Serge48253707"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921601519842996449", "timestamp": "2025-05-11T16:21:18+00:00", "timestamp_unix": 1746980478, "type": "comment", "text": "@SDA_Eventos @DanieleGiova50 Pd significa PADOVA. (scritto nella descrizione del profilo). Che poi, chissà cosa minchia c’entra con la medicina. Almeno la comprensione di quello di cui parli, la prossima volta. \n\nPs. Fammi vedere l’esito della segnalazione ti prego, voglio farmi la mia risata quotidiana.", "context": {"type": "tweet", "id": "1921388159696318822", "text": "@iPat_Pd @DanieleGiova50 PD nel tuo Nick la dice lunga. Bye bye troll. Post segnalato", "author_id": "1896779599016513536", "author_username": "SDA_Eventos"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1920836723795218915", "tweet_id": "1921601617519837681", "timestamp": "2025-05-11T16:21:41+00:00", "timestamp_unix": 1746980501, "type": "comment", "text": "@SDA_Eventos @DanieleGiova50 E la prossima volta cerca di scrivere qualcosa di intelligente, anche se capisco benissimo la difficoltà.", "context": {"type": "tweet", "id": "1921388159696318822", "text": "@iPat_Pd @DanieleGiova50 PD nel tuo Nick la dice lunga. Bye bye troll. Post segnalato", "author_id": "1896779599016513536", "author_username": "SDA_Eventos"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1924356534349214111", "tweet_id": "1924377215061434729", "timestamp": "2025-05-19T08:10:55+00:00", "timestamp_unix": 1747642255, "type": "comment", "text": "@matteosalvinimi @AndreCVentura Parlaci un po’ di questo Salvini. https://t.co/TtLl5EHt7Q", "context": {"type": "tweet", "id": "1924473095462568437", "text": "https://t.co/TFu7yAaXn0", "author_id": "1520081244314345472", "author_username": "sirio61"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 30, "quote_count": 0, "view_count": 269}}]}