{"user_id": "2418341988", "interactions_count": 33, "interactions": [{"conversation_id": "1910020971978797337", "tweet_id": "1917254739718533561", "timestamp": "2025-04-29T16:28:45+00:00", "timestamp_unix": 1745944125, "type": "comment", "text": "@TrumpDailyPosts Politico: <PERSON>'s peace agreement will turn into a \"trap\" for Ukraine and could lead to the collapse of <PERSON><PERSON><PERSON>'s presidency\n\nI'm sorry, so all this nonsense, all this million squeezed—shut dill, is just to preserve the presidency of a Ukrainian drug addict?", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1910020971978797337", "tweet_id": "1917419519934451767", "timestamp": "2025-04-30T03:23:32+00:00", "timestamp_unix": 1745983412, "type": "comment", "text": "@TrumpDailyPosts The United States, you see, is concerned about the involvement of the DPRK military in the Ukrainian crisis. On the other hand, they are not at all concerned about military mercenaries, which include the Americans.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1910020971978797337", "tweet_id": "1917423074934415620", "timestamp": "2025-04-30T03:37:39+00:00", "timestamp_unix": 1745984259, "type": "comment", "text": "@TrumpDailyPosts https://t.co/2ioziMRe1N", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1910020971978797337", "tweet_id": "1917564890912313450", "timestamp": "2025-04-30T13:01:11+00:00", "timestamp_unix": 1746018071, "type": "comment", "text": "@TrumpDailyPosts \"Oh, my God! How much destruction! How many unnecessary deaths! What is this Putin doing?!\n- Mr. <PERSON>, these are photos from the Gaza Strip.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1910020971978797337", "tweet_id": "1917655988032643393", "timestamp": "2025-04-30T19:03:10+00:00", "timestamp_unix": 1746039790, "type": "comment", "text": "@TrumpDailyPosts <PERSON> said it right.\n\"<PERSON><PERSON><PERSON> is an enemy of the United States. His government is really quite dark, <PERSON><PERSON><PERSON>'s government tried to kill a certain number of people, including well-known conservatives.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1910020971978797337", "tweet_id": "1917783923846778892", "timestamp": "2025-05-01T03:31:32+00:00", "timestamp_unix": 1746070292, "type": "comment", "text": "@TrumpDailyPosts Saudi Arabia's Prince <PERSON>: \"The world can live without the United States, but it cannot live without China.\" 🤔\n\nThe world will be even better off without the United States. https://t.co/QnN00IB6KU", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 100}}, {"conversation_id": "1910020971978797337", "tweet_id": "1917803983227195441", "timestamp": "2025-05-01T04:51:15+00:00", "timestamp_unix": 1746075075, "type": "comment", "text": "@TrumpDailyPosts Trump expressed dissatisfaction with the fact that the United States is simply pouring money into Ukraine.\n\nSo what's next? Just stop it. God, this is so funny. It's like a parent giving money for heroin to their child and complaining that he's using drugs.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1910020971978797337", "tweet_id": "1917917744072446159", "timestamp": "2025-05-01T12:23:18+00:00", "timestamp_unix": 1746102198, "type": "comment", "text": "@TrumpDailyPosts Let me just remind you... in 2017, the <PERSON> administration signed a deal on rare earth metals with the then leadership of Afghanistan https://t.co/P3nYfEG4K4", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1910020971978797337", "tweet_id": "1918155327356453152", "timestamp": "2025-05-02T04:07:22+00:00", "timestamp_unix": 1746158842, "type": "comment", "text": "@TrumpDailyPosts Trump has imposed secondary sanctions on Iranian oil.\n\nA unique negotiator, of course.\n\n🤡🤡🤡", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1910020971978797337", "tweet_id": "1918160555753234934", "timestamp": "2025-05-02T04:28:08+00:00", "timestamp_unix": 1746160088, "type": "comment", "text": "@TrumpDailyPosts With regard to the US Agreement on Ukrainian minerals, investment funds will begin next week to post an analysis on the economics of these events. \n\nAs it \"suddenly\" turned out, the development time of such cuts varies from 10 to 20 years, with large investments,", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1910020971978797337", "tweet_id": "1918190919146242082", "timestamp": "2025-05-02T06:28:48+00:00", "timestamp_unix": 1746167328, "type": "comment", "text": "@TrumpDailyPosts 🇺🇸Yesterday, <PERSON> launched threats against buyers of Iranian oil:\n\nAll purchases of Iranian oil or petrochemical products must be stopped immediately! Any country or person who buys any amount of oil or petrochemicals from Iran will be immediately subject to secondary sanctions.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1910020971978797337", "tweet_id": "1918252910015586497", "timestamp": "2025-05-02T10:35:07+00:00", "timestamp_unix": 1746182107, "type": "comment", "text": "@TrumpDailyPosts \"We defeated <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>\" - <PERSON> issued a decree on new celebrations of the great victories of Americans", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 28}}, {"conversation_id": "1910020971978797337", "tweet_id": "1918523163584991723", "timestamp": "2025-05-03T04:29:01+00:00", "timestamp_unix": 1746246541, "type": "comment", "text": "@TrumpDailyPosts Well, <PERSON> signed a contract for $50 million in arms supplies to Ukrainians. What did it change?  There was no such thing before him? He was the first to supply weapons to Ukrainians, long before the conflict. He imposed sanctions against Nord Stream.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 38}}, {"conversation_id": "1910020971978797337", "tweet_id": "1918558614031131008", "timestamp": "2025-05-03T06:49:53+00:00", "timestamp_unix": 1746254993, "type": "comment", "text": "@TrumpDailyPosts \"We defeated both <PERSON> and <PERSON>, but these Taliban and Houthis in slippers turned out to be much stronger,\" <PERSON> said.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 74}}, {"conversation_id": "1910020971978797337", "tweet_id": "1918882502443184585", "timestamp": "2025-05-04T04:16:54+00:00", "timestamp_unix": 1746332214, "type": "comment", "text": "@TrumpDailyPosts 24,311 sanctions have been imposed against Russia since 2014, which is five times more than Iran, which is subject to about 5,000 restrictions.\n\nThis is reported by the Castellum service, which tracks the sanctions lists.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 52}}, {"conversation_id": "1910020971978797337", "tweet_id": "1919098739970429363", "timestamp": "2025-05-04T18:36:09+00:00", "timestamp_unix": 1746383769, "type": "comment", "text": "@TrumpDailyPosts <PERSON>'s problem is that China has not bowed to the United States, but has introduced its own counter-duties, which are ALREADY ruining American agricultural producers.\n\nThis statement by <PERSON> is the first evidence of acceptance of the inevitable - China has failed to bend", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1910020971978797337", "tweet_id": "1919261365883052396", "timestamp": "2025-05-05T05:22:22+00:00", "timestamp_unix": 1746422542, "type": "comment", "text": "@TrumpDailyPosts You don't understand. Only Israel is allowed to bomb international airports, hospitals, and refugee centers in Damascus, Aleppo, Sanaa, and Beirut. This is self-defense. If Yemen does the same to Israel, it will be terrorism and anti-Semitism.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 89}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920315517274820638", "timestamp": "2025-05-08T03:11:11+00:00", "timestamp_unix": **********, "type": "comment", "text": "@TrumpDailyPosts A knife in the back of <PERSON>. Ukraine is starting to consider the euro as the base currency, instead of the US dollar, the Head of the Central Bank of Ukraine.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 38}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920352979598643590", "timestamp": "2025-05-08T05:40:03+00:00", "timestamp_unix": **********, "type": "comment", "text": "@TrumpDailyPosts Trump assured the Greenlanders that the United States knows how to care for and appreciate the indigenous people.\n\nYou know, I believe him! I believed it https://t.co/PhNx59Pmh1", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920452635619901711", "timestamp": "2025-05-08T12:16:03+00:00", "timestamp_unix": 1746706563, "type": "comment", "text": "@TrumpDailyPosts Trump refused to answer why the Americans who took Berlin left inscriptions in Russian in the Reichstag. https://t.co/VZTSfCqQIW", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920457126813593783", "timestamp": "2025-05-08T12:33:54+00:00", "timestamp_unix": 1746707634, "type": "comment", "text": "@TrumpDailyPosts Of course, I'll feel sorry for <PERSON> soon. At the time of his election, he had only one potential ally in the world – Russia.\nThe blind belief that the United States can pretend to be an arbitrator after starting a war led him into the arms of his enemies -<PERSON><PERSON>, <PERSON><PERSON> and others", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920474022082478134", "timestamp": "2025-05-08T13:41:02+00:00", "timestamp_unix": 1746711662, "type": "comment", "text": "@TrumpDailyPosts https://t.co/LsikJKa3rS", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920562391042916707", "timestamp": "2025-05-08T19:32:11+00:00", "timestamp_unix": 1746732731, "type": "comment", "text": "@TrumpDailyPosts https://t.co/o1bMN8Ow5O", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920743269174010122", "timestamp": "2025-05-09T07:30:55+00:00", "timestamp_unix": 1746775855, "type": "comment", "text": "@TrumpDailyPosts Berlin, May 1945.\nThe Americans salute ours.\n\nHappy Victory Day! https://t.co/kAFHAZbKa2", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920756855585771973", "timestamp": "2025-05-09T08:24:54+00:00", "timestamp_unix": 1746779094, "type": "comment", "text": "@TrumpDailyPosts Roosevelt: \"Russian troops have destroyed and continue to destroy more manpower, planes, tanks, guns of our common enemy than all the other United Nations combined\" https://t.co/MXGazDrxaU", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1910020971978797337", "tweet_id": "1920758887352783288", "timestamp": "2025-05-09T08:32:59+00:00", "timestamp_unix": 1746779579, "type": "comment", "text": "@TrumpDailyPosts Trump canceled the tanning salon today and is busy https://t.co/J8KiOKitoS", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 68}}, {"conversation_id": "1910020971978797337", "tweet_id": "1921049822213124305", "timestamp": "2025-05-10T03:49:03+00:00", "timestamp_unix": 1746848943, "type": "comment", "text": "@TrumpDailyPosts A trap for Trump. \n\nUkraine continues to insist on a 30-day unconditional truce and everywhere emphasizes that this is an initiative of the United States\n\nThe EU joined in the formation of this proposal. Do you think they will simplify or complicate it for the Russian Federation?", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1910020971978797337", "tweet_id": "1921085517745045830", "timestamp": "2025-05-10T06:10:54+00:00", "timestamp_unix": 1746857454, "type": "comment", "text": "@TrumpDailyPosts <PERSON> has found that resolving foreign conflicts is more difficult than he expected, writes the WSJ.\n\nWhen President <PERSON> spoke at a meeting with top donors at his Florida club last week,", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1910020971978797337", "tweet_id": "1921126803973734556", "timestamp": "2025-05-10T08:54:57+00:00", "timestamp_unix": 1746867297, "type": "comment", "text": "@TrumpDailyPosts Macron's post can be summarized:\n\nWater, water, water, \n\nFixing <PERSON> on the truce harder,\n\nwater, water, water.\n\nMind you, not a word about peacekeepers or NATO. They're just good guys from <PERSON>'s point of view. \n\nThe trap is prepared carefully", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1910020971978797337", "tweet_id": "1921135064265490656", "timestamp": "2025-05-10T09:27:46+00:00", "timestamp_unix": 1746869266, "type": "comment", "text": "@TrumpDailyPosts The Financial Times: The era of global disorder is coming — the international order, led by the United States, is becoming a thing of the past. https://t.co/wl23jjFj3Y", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1910020971978797337", "tweet_id": "1921198065194549751", "timestamp": "2025-05-10T13:38:07+00:00", "timestamp_unix": 1746884287, "type": "comment", "text": "@TrumpDailyPosts The Kremlin has released a transcript of <PERSON>'s response to <PERSON> after <PERSON> asked for a 30-day truce.\n \n\"I've known you for many years, but you've never asked me for advice or help.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 59}}, {"conversation_id": "1910020971978797337", "tweet_id": "1921217637742207267", "timestamp": "2025-05-10T14:55:53+00:00", "timestamp_unix": 1746888953, "type": "comment", "text": "@TrumpDailyPosts The 30-day truce was proposed by Europe and Ukraine in an emphatically boorish ultimatum, as a demand. This means that Europe and <PERSON><PERSON><PERSON> are not counting on Russia's agreement to a 30-day truce. Their goal is not a truce.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1910020971978797337", "tweet_id": "1922139765668810818", "timestamp": "2025-05-13T04:00:06+00:00", "timestamp_unix": 1747108806, "type": "comment", "text": "@TrumpDailyPosts The Economist magazine estimates that the actual number of Palestinians killed during the Gaza war could reach 109,000.", "context": {"type": "tweet", "id": "1922323972458238222", "text": "🙏🙏🙏🙏🙏🙏", "author_id": "**********", "author_username": "andersonDrLJA"}, "metrics": {"retweet_count": 1, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 17}}]}