{"user_id": "1745194752021049344", "interactions_count": 28, "interactions": [{"conversation_id": "1921964988371021872", "tweet_id": "1921966672350462119", "timestamp": "2025-05-12T16:32:17+00:00", "timestamp_unix": 1747067537, "type": "comment", "text": "@CGasparino So it’s <PERSON><PERSON> fault the markets drop but when they come roaring back it’s nothing to do with him. Is basically what you are saying.", "context": {"type": "tweet", "id": "1922315086825226244", "text": "@USTradeRep https://t.co/jzqPhw6c0z", "author_id": "96365363", "author_username": "bluescat47"}, "metrics": {"retweet_count": 1, "reply_count": 36, "like_count": 64, "quote_count": 2, "view_count": 3635}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921967924652150973", "timestamp": "2025-05-12T16:37:16+00:00", "timestamp_unix": 1747067836, "type": "comment", "text": "@DexyBug @CGasparino It’s not but I’m not wasting time explaining it to you.", "context": {"type": "tweet", "id": "1921967495247802819", "text": "@Americanmight5 @CGasparino That’s quite true. They dropped because of his trade war and the ensuing confusion and nonsensical “liberation day” fiasco. They are rising in spite of him (and primarily due to <PERSON><PERSON> trying to clean up his mess).", "author_id": "1180224779950817280", "author_username": "DexyBug"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 56}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921969496098525480", "timestamp": "2025-05-12T16:43:30+00:00", "timestamp_unix": 1747068210, "type": "comment", "text": "@lwaysITM @CGasparino So you are in the camp of letting China rip us off and destroy what’s left of our manufacturing in this nation. Got it, glad we are clear on that.", "context": {"type": "tweet", "id": "1921968453499412990", "text": "@Americanmight5 @CGasparino yes. it's his fault that the market dropped and many got hurt badly. he sure as hell doesn't deserve credit for the market's comeback. all he did is \"fix\" his fuck-up and bring us back to square 1 (although not quite).", "author_id": "1569345327144091648", "author_username": "lwaysITM"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 172}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921969803520065831", "timestamp": "2025-05-12T16:44:44+00:00", "timestamp_unix": 1747068284, "type": "comment", "text": "@Rightsof_Man @CGasparino So what you are saying is it’s ok for China to have our heads in a vice but when we did the same thing it’s bad news? Where do you people come from?", "context": {"type": "tweet", "id": "1921968098921333224", "text": "@Americanmight5 @CGasparino I honestly don't mean to sound crude, but as an analogy....\n\nIf I put your head in a vice grip where I am almost exploding your skull, are you going to give me credit when I relieve the pressure?", "author_id": "808768479872643081", "author_username": "Rightsof_Man"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 187}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921969955576143985", "timestamp": "2025-05-12T16:45:20+00:00", "timestamp_unix": 1747068320, "type": "comment", "text": "@DrBobbyDigital @CGasparino So China wasn’t an issue and everything was good before this is what you are saying?", "context": {"type": "tweet", "id": "1921968579504718085", "text": "@Americanmight5 @CGasparino It was his fault and he undid the thing that he did so yeah I guess you could give him credit for fixing the problem he caused.", "author_id": "330608779", "author_username": "DrBobbyDigital"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 38}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921970087323386264", "timestamp": "2025-05-12T16:45:51+00:00", "timestamp_unix": 1747068351, "type": "comment", "text": "@Texas70WB @CGasparino Too bad he isn’t right which is par for the course with the vast majority of people on here.", "context": {"type": "tweet", "id": "1922315086825226244", "text": "@USTradeRep https://t.co/jzqPhw6c0z", "author_id": "96365363", "author_username": "bluescat47"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921970468875100224", "timestamp": "2025-05-12T16:47:22+00:00", "timestamp_unix": 1747068442, "type": "comment", "text": "@Ravenwild9 @CGasparino Was China a problem before this and were they hurting this nation with what they were doing? Would you agree or disagree with needing to reset with China or were you ok with how things were?", "context": {"type": "tweet", "id": "1921969638486782408", "text": "@Americanmight5 @CGasparino It’s both.", "author_id": "1096996326292676609", "author_username": "Ravenwild9"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 134}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921970933981393114", "timestamp": "2025-05-12T16:49:13+00:00", "timestamp_unix": 1747068553, "type": "comment", "text": "@brentmulligan @lwaysITM @CGasparino What would you consider critical and non critical? There are plenty of small towns middle America that would benefit from what you call low value manufacturing, the only reason it’s nostalgia is due to all those jobs being shifted to slave labor.", "context": {"type": "tweet", "id": "1921970357126496416", "text": "It only makes sense to bring back critical manufacturing. The jobs are higher paying and it is by its nature critical. We don’t need to bring back the low value mfg. We do not have minimum wage labor capacity or a collective desire to pay 3-4x for everyday items.\n\nNostalgia for a beautiful time when everyone worked in factories and their kids worked in the same factories is not a sufficient reason to roll the entire country backwards.", "author_id": "19626867", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 85}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921971777678241978", "timestamp": "2025-05-12T16:52:34+00:00", "timestamp_unix": 1747068754, "type": "comment", "text": "By MAGA you mean blue collar factory workers that no longer exist due to work being shifted away. No one who works for a living gives a crap what economist say or do. The economy wasn’t working for the middle class and Trump is fixing that. Trade deficits are not good for the majority of Americans, as for having us in a vice, do you remember <PERSON><PERSON>?", "context": {"type": "tweet", "id": "1921970902150824035", "text": "@Americanmight5 @CGasparino MAGA went to war with 192 countries with fake numbers while crying every country is screwing us over because they believe trade deficits are bad.  \n\nNo economist supports this. \n\nHow does China have us in vice? Last time American soybean farmers lost permanent markets. Bravo!", "author_id": "808768479872643081", "author_username": "Rightsof_Man"}, "metrics": {"retweet_count": 0, "reply_count": 4, "like_count": 1, "quote_count": 0, "view_count": 80}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921972668460380288", "timestamp": "2025-05-12T16:56:07+00:00", "timestamp_unix": 1747068967, "type": "comment", "text": "@AncloteYakker @CGasparino Got it so you were not around or old enough to remember <PERSON><PERSON> huh? Why is building and maintaining things in this nation a problem for you?", "context": {"type": "tweet", "id": "1921971358121984120", "text": "@Americanmight5 @CGasparino Would you pat an arsonist on the back after he puts out his own fire? https://t.co/7gDDUkY3nE", "author_id": "1868780198520299520", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 78}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921973630684041367", "timestamp": "2025-05-12T16:59:56+00:00", "timestamp_unix": 1747069196, "type": "comment", "text": "You are suggesting that rising bond yields signal trouble potentially pointing to inflation fears or economic uncertainty? Which is one way to look at it but I can most definitely make an argument that it can also indicate growing confidence in the U.S. economy under <PERSON>’s policies.", "context": {"type": "tweet", "id": "1921970859993858464", "text": "@Americanmight5 @CGasparino No, Market is moving despite <PERSON>’s brain-dead, protectionist, central planning.  \n\nBTW, have a look at bond yields.  What’s the telling you?", "author_id": "703987546066460676", "author_username": "MikeMikewelch3"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 51}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921975544066740515", "timestamp": "2025-05-12T17:07:32+00:00", "timestamp_unix": 1747069652, "type": "comment", "text": "@Rightsof_Man @CGasparino Deficits in general are bad hence the word. Free and fair trade fixes most of our issues and trading is good for both economy’s we shouldn’t ever be running a deficit. If we can grow and sell in country it’s always a better option if monetarily viable.", "context": {"type": "tweet", "id": "1921972723145736402", "text": "@Americanmight5 @CGasparino Trade deficits are not necessarily good or bad. I run a trade deficit with my barber and better off for it.  US runs a trade deficit with Costa Rica since bananas are better grown there.  Do want to use valuable resources to grow bananas and coffee in the USA?", "author_id": "808768479872643081", "author_username": "Rightsof_Man"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 60}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921978317827104984", "timestamp": "2025-05-12T17:18:34+00:00", "timestamp_unix": 1747070314, "type": "comment", "text": "Higher yields, like the spike from 3.9% to 4.5% for 10-year U.S. Treasury notes often reflect expectations of stronger economic growth and higher future interest rates as the economy heats up. \n\n<PERSON>’s tariff strategy, though disruptive in the short term, is designed to bring manufacturing jobs back to the U.S. the goal is to bring jobs and manufacturing back to the United States, raising wages, increasing revenues and reviving the American Dream for a lot of people that have been left behind including our children. \n\nFor example, during <PERSON>’s first term, his tax cuts and deregulation policies contributed to GDP growth of 2.5% and the markets rose nearly 40% from 2017 to 2020 despite trade tensions. \n\nThe current market rally suggests investors are betting on a similar outcome. <PERSON>’s tariffs may create short-term volatility, but his broader economic agenda, including energy competitiveness and tax cuts will fuel long-term growth. \n\nRising bond yields might not be a sign of distress but rather a vote of confidence in a stronger U.S. economy under <PERSON>, as investors demand higher returns in anticipation of robust growth and a tighter monetary policy from the Federal Reserve to keep inflation in check.\n\n<PERSON>’s willingness to pause tariffs after noticing bond investors S<PERSON>rrelly shows he’s pragmatic, not reckless. \n\nThis flexibility demonstrates responsiveness to market signals while still pursuing his goal of reciprocal trade. The bond market’s reaction, then, could be less about fear and more about adjusting to a new economic reality where the U.S. prioritizes domestic industry, an approach that could strengthen the economy long-term, even if it means short-term adjustments in borrowing.", "context": {"type": "tweet", "id": "1921974606056796276", "text": "@Americanmight5 @CGasparino Ok.   Make it.\n\nHow is the bond market getting this wrong?", "author_id": "703987546066460676", "author_username": "MikeMikewelch3"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 57}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921980008660177145", "timestamp": "2025-05-12T17:25:17+00:00", "timestamp_unix": 1747070717, "type": "comment", "text": "@rustysmifmachi1 @AncloteYakker @CGasparino Maybe reread what I said, make sure you wear your gloves too. You are so lost you don’t have a clue what you are even talking about. The media and meds have damaged you beyond repair that is for sure.", "context": {"type": "tweet", "id": "1922315086825226244", "text": "@USTradeRep https://t.co/jzqPhw6c0z", "author_id": "96365363", "author_username": "bluescat47"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921981886567436387", "timestamp": "2025-05-12T17:32:45+00:00", "timestamp_unix": 1747071165, "type": "comment", "text": "Trade deficits can hurt when they’re too large, signaling over-reliance on foreign goods, which kills local jobs 3.7 million ish lost to China’s deficit from 2001-2018 I’m sure it’s even higher the last few years. They also weaken our manufacturing base, making us vulnerable to supply chain disruptions, like during COVID. Capital inflows don’t always offset the damage, and we have recent examples like <PERSON>vid to prove that. Imagine something like a war, how much worse it would be.", "context": {"type": "tweet", "id": "1921977728015683857", "text": "@Americanmight5 @CGasparino A trade deficit is not necessarily bad. You personally run trade deficits all the time (grocery store, barber, mechanic) and better for it. \n\nA strong economy naturally produces a trade deficit.  \n\nOn the flip side we get more capital investments flowing in.", "author_id": "808768479872643081", "author_username": "Rightsof_Man"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921982187395502577", "timestamp": "2025-05-12T17:33:56+00:00", "timestamp_unix": 1747071236, "type": "comment", "text": "@TomReevesMBA @Rightsof_Man @CGasparino It’s funny to me that you want to chime in but only to insult, I bet that <PERSON> really helps you when it comes to voltage resistance.", "context": {"type": "tweet", "id": "1921978594504409252", "text": "@Rightsof_Man @Americanmight5 @CGasparino You're trying to explain a 9 volt concept to someone that has a 6 volt brain.", "author_id": "1912325832263233539", "author_username": "TomReevesMBA"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921983084896907707", "timestamp": "2025-05-12T17:37:30+00:00", "timestamp_unix": 1747071450, "type": "comment", "text": "@rustysmifmachi1 @AncloteYakker @CGasparino The fact you are citing cbs as your go to shows me everything I need to know about you. Does typing in caps help you feel like a real man? So many beta want to be finance guys like you on here make me laugh.  You won’t ever mature or understand.", "context": {"type": "tweet", "id": "1922315086825226244", "text": "@USTradeRep https://t.co/jzqPhw6c0z", "author_id": "96365363", "author_username": "bluescat47"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921983546777813019", "timestamp": "2025-05-12T17:39:20+00:00", "timestamp_unix": 1747071560, "type": "comment", "text": "@smallheath631 @CGasparino Funny did you steal that from the last guy who just said it? I’m sure you were ok with China then is what you are saying and are in favor of them ripping us off and using child/slave labor for all those useless things you want?", "context": {"type": "tweet", "id": "1921977825596182781", "text": "@Americanmight5 @CGasparino It’s like the arsonist who sets the fire who wants credit when he returns to the scene and puts it out.", "author_id": "740606269594206209", "author_username": "smallheath631"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 120}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921984910627164342", "timestamp": "2025-05-12T17:44:46+00:00", "timestamp_unix": 1747071886, "type": "comment", "text": "Just to be clear because I have a concise and valid argument for my point, I must be using AI? It’s  amusing that you think I need AI for stats or to argue my stance. I was navigating markets and investing long before you were around. Real-world experience, not just talk, unlike most folks like you here. People like you will never understand what I’m talking about because you look at the markets inherently through a Trump is evil stance and are not able to get over your own shortsightedness.", "context": {"type": "tweet", "id": "1921982961966104837", "text": "@Americanmight5 @CGasparino The magic of AI to cut and paste is a wondrous thing.", "author_id": "703987546066460676", "author_username": "MikeMikewelch3"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921985727010640126", "timestamp": "2025-05-12T17:48:00+00:00", "timestamp_unix": 1747072080, "type": "comment", "text": "@rustysmifmachi1 @AncloteYakker @CGasparino Another anti-Trump non-real media outlet great sources there. <PERSON> couldn’t find his way out of a paper bag much less form a valid arguments on anything money/market related or the economy in general. The guy was broken mentally over 10 years ago.", "context": {"type": "tweet", "id": "1922315086825226244", "text": "@USTradeRep https://t.co/jzqPhw6c0z", "author_id": "96365363", "author_username": "bluescat47"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 28}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921986132092293578", "timestamp": "2025-05-12T17:49:37+00:00", "timestamp_unix": **********, "type": "comment", "text": "@connie_mauricio @CGasparino You didn’t have an issue with anything China was doing before this trade war? That is very telling on your mental health. What about IP theft why don’t we start there you were ok with the IP theft?", "context": {"type": "tweet", "id": "1921982840129953971", "text": "@Americanmight5 @CGasparino Right, because nothing was wrong with trade until trump stepped in.", "author_id": "1433544641064824833", "author_username": "connie_mauricio"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 90}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921987559984333006", "timestamp": "2025-05-12T17:55:17+00:00", "timestamp_unix": **********, "type": "comment", "text": "@smallheath631 @CGasparino If it was just “trade” as you say it wouldn’t be an issue. Explain to me why we are not able to sell products in China. Explain to me why it’s OK for China to run almost a $450 billion a year deficit on trade with us, let’s start there.", "context": {"type": "tweet", "id": "1921985504884490342", "text": "@Americanmight5 @CGasparino How is China ripping us off? They produce products that are desired by US consumers, who voluntarily pay for those products. It’s called trade.", "author_id": "740606269594206209", "author_username": "smallheath631"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921987884153708734", "timestamp": "2025-05-12T17:56:35+00:00", "timestamp_unix": 1747072595, "type": "comment", "text": "@MikeMikewelch3 @CGasparino I think I’m wasting my time here. Reread what I said at some point and maybe you’ll comprehend a 10th of it.", "context": {"type": "tweet", "id": "1921985830656016698", "text": "@Americanmight5 @CGasparino Concise?  Try again.  \n\nThis book has helped cure thousands of people who also suffer from the delusions of central planning and promises of big government . https://t.co/qx5gaV1Ubx", "author_id": "703987546066460676", "author_username": "MikeMikewelch3"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921988244134150226", "timestamp": "2025-05-12T17:58:00+00:00", "timestamp_unix": 1747072680, "type": "comment", "text": "@uormatthews @CGasparino So basically, you’re telling me you’re OK with China being in control of trade and everything was great with China prior to this? Come back to me when you have a grasp on the basics and we will talk.", "context": {"type": "tweet", "id": "1921986744120975649", "text": "@Americanmight5 @CGasparino You mean if someone makes a mistake, then stops doing that thing you want to give them credit for it?", "author_id": "471830282", "author_username": "<PERSON>ormatthe<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 5, "like_count": 1, "quote_count": 0, "view_count": 113}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921989191056900562", "timestamp": "2025-05-12T18:01:46+00:00", "timestamp_unix": 1747072906, "type": "comment", "text": "What did <PERSON> fail and lose? As for legit journalism, I would say any of the three letter agencies or major media outlets are no longer trustworthy. I’d say you’d have to look for yourself and decide what you believe in what you don’t believe. If you think I trust Fox News any more than CBS you would be mistaken they all lie and have an agenda to fill.", "context": {"type": "tweet", "id": "1922315086825226244", "text": "@USTradeRep https://t.co/jzqPhw6c0z", "author_id": "96365363", "author_username": "bluescat47"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1921964988371021872", "tweet_id": "1921990152433631724", "timestamp": "2025-05-12T18:05:35+00:00", "timestamp_unix": 1747073135, "type": "comment", "text": "You have a real life example through <PERSON><PERSON> to show just how wrong you are. What happens when China decides to just turn off the exports or jack the price up to the point nothing is affordable? When you are subservient to another nation for survival, you are no longer free and independent.", "context": {"type": "tweet", "id": "1921988329500770650", "text": "@Americanmight5 @CGasparino Because, unlike China, the US is not reliant on an economy based on exports. The Chinese do not consume in the same way Americans do. They are savers. Running a trade deficit is a non-issue.", "author_id": "740606269594206209", "author_username": "smallheath631"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1921964988371021872", "tweet_id": "1922000282470236248", "timestamp": "2025-05-12T18:45:51+00:00", "timestamp_unix": 1747075551, "type": "comment", "text": "@smallheath631 @CGasparino Correct which is why it should and will be fair going forward that’s the whole point of this. If it was fair none of this would have happened. I fail to see what you are actually arguing for just not liking <PERSON> isn’t an argument.", "context": {"type": "tweet", "id": "1921990594747892054", "text": "@Americanmight5 @CGasparino They are just as reliant on us purchasing their goods as we are on needing them. It’s a mutually beneficial relationship.", "author_id": "740606269594206209", "author_username": "smallheath631"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1921964988371021872", "tweet_id": "1922000709202878855", "timestamp": "2025-05-12T18:47:32+00:00", "timestamp_unix": 1747075652, "type": "comment", "text": "@rustysmifmachi1 @AncloteYakker @CGasparino Resetting trade isn’t failing. Try to keep your blood pressure down. I don’t need to be paying for any more hospital visits. You can’t see the good through the hate, it’s your loss. Guys like you will aways be losers there isn’t any way around it.", "context": {"type": "tweet", "id": "1922315086825226244", "text": "@USTradeRep https://t.co/jzqPhw6c0z", "author_id": "96365363", "author_username": "bluescat47"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 8}}]}