{"user_id": "1845981319043428352", "interactions_count": 28, "interactions": [{"conversation_id": "1896793893301285325", "tweet_id": "1897129487328039000", "timestamp": "2025-03-05T03:38:11+00:00", "timestamp_unix": 1741145891, "type": "comment", "text": "@KevinMankind @Yuanzzxxmm @HelenXie @__Inty__ 不用打了，反正已经制裁了", "context": {"type": "tweet", "id": "1896881787382731017", "text": "@Yuanzzxxmm @HelenXie @__Inty__ 呵呵，中国的华大和它的专利官司打得有来有往", "author_id": "331507608", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1896793893301285325", "tweet_id": "1897129611521352170", "timestamp": "2025-03-05T03:38:41+00:00", "timestamp_unix": 1741145921, "type": "comment", "text": "@HelenXie @__Inty__ 说明已经可以国产化替代，当然美国人少赚钱", "context": {"type": "tweet", "id": "1896834681464930522", "text": "@__Inty__ 唉，不知道谁遭殃", "author_id": "1498453182623584256", "author_username": "<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 54}}, {"conversation_id": "1896793893301285325", "tweet_id": "1897129865515782204", "timestamp": "2025-03-05T03:39:41+00:00", "timestamp_unix": 1741145981, "type": "comment", "text": "@wairicky1 @__Inty__ 也不这么看，中国去年总贸易出口3.6万亿美元，美国只是个零头 对美国的贸易顺差可以忽略不计", "context": {"type": "tweet", "id": "1896889950945382482", "text": "@__Inty__ 其实看贸易逆差就知道边个伤害大😂😂", "author_id": "1507664570227490822", "author_username": "wairicky1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 223}}, {"conversation_id": "1896793893301285325", "tweet_id": "1897130041009693127", "timestamp": "2025-03-05T03:40:23+00:00", "timestamp_unix": 1741146023, "type": "comment", "text": "@HsinYaoWangMD @__Inty__ 所以制裁了，加速高端产业国产化", "context": {"type": "tweet", "id": "1896922636669829479", "text": "@__Inty__ 現在國內有很多pore based seq solution，二代的話華大基因的技術也不比illumina差，不認為依賴度有很高", "author_id": "1445698701976997891", "author_username": "HsinYaoWangMD"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1896793893301285325", "tweet_id": "1897130355095953558", "timestamp": "2025-03-05T03:41:38+00:00", "timestamp_unix": 1741146098, "type": "comment", "text": "@zyushnghu1 @__Inty__ 当过殖民地的人思维就是不一样", "context": {"type": "tweet", "id": "1897262231236342014", "text": "苦了两边的消费者", "author_id": "1764490316189704192", "author_username": "Ricarda2023"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1897499256157622557", "tweet_id": "1897514427374772494", "timestamp": "2025-03-06T05:07:48+00:00", "timestamp_unix": 1741237668, "type": "comment", "text": "@tommyshine61281 @RealYanbo @Neo_Nalanda @bbcchinese 反贼不是中国人，别逼逼 我们中国人都支持他", "context": {"type": "tweet", "id": "1897510603004240300", "text": "@RealYanbo @Neo_Nalanda @bbcchinese 你是红蛆，你是玩意，你代表不了别人，别瞎几把我们。。。", "author_id": "1766458396730138624", "author_username": "tommyshine61281"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1897499256157622557", "tweet_id": "1897514494152380820", "timestamp": "2025-03-06T05:08:04+00:00", "timestamp_unix": 1741237684, "type": "comment", "text": "@tommyshine61281 @RealYanbo @Neo_Nalanda @bbcchinese 你不是中国人", "context": {"type": "tweet", "id": "1897511188176756738", "text": "@RealYanbo @Neo_Nalanda @bbcchinese 放你妈的屁，老子是中国人，红蛆不配做中国人，因为压根就不配做人", "author_id": "1766458396730138624", "author_username": "tommyshine61281"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1897499256157622557", "tweet_id": "1897514700289810699", "timestamp": "2025-03-06T05:08:53+00:00", "timestamp_unix": 1741237733, "type": "comment", "text": "@jamesguo007 @bbcchinese 真心感谢，14亿人给你点赞", "context": {"type": "tweet", "id": "1897509723047649524", "text": "@bbcchinese 馬上凍結所有中共貪官在海外資產，情婦一律驅逐立馬就老實了。", "author_id": "1644733427130966018", "author_username": "jamesguo007"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 69}}, {"conversation_id": "1897499256157622557", "tweet_id": "1897514920910184850", "timestamp": "2025-03-06T05:09:45+00:00", "timestamp_unix": 1741237785, "type": "comment", "text": "@BagBoy613 @bbcchinese 对中国加关税了，和台湾逆来顺受不一样", "context": {"type": "tweet", "id": "1897500559076110586", "text": "@bbcchinese 中國大使 製造混亂 莫名其妙？", "author_id": "1713823852370055168", "author_username": "BagBoy613"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 36}}, {"conversation_id": "1908118213310767344", "tweet_id": "1908163168171639052", "timestamp": "2025-04-04T14:22:05+00:00", "timestamp_unix": 1743776525, "type": "comment", "text": "@Rashomon725 @zaobaosg 中国对美国出口占中国总GDP的2.7％  说有影响一点点，说没有影响往其它地方平均一下 也没有影响", "context": {"type": "tweet", "id": "1908119529747841400", "text": "@zaobaosg 我沒有料到中國會這麼硬，就看中美兩國誰先扛不住了", "author_id": "1225999521127305217", "author_username": "Rashomon725"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 174}}, {"conversation_id": "1910577588533149854", "tweet_id": "1910680110757556245", "timestamp": "2025-04-11T13:03:31+00:00", "timestamp_unix": 1744376611, "type": "comment", "text": "@sccatke1 @zaobaosg 狗怎么能听懂人话 是吧，你能听懂？", "context": {"type": "tweet", "id": "1910608463291654598", "text": "@zaobaosg 它们自己说的话自己都不相信呢", "author_id": "3131713622", "author_username": "sccatke1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1910577588533149854", "tweet_id": "1910680284967952420", "timestamp": "2025-04-11T13:04:13+00:00", "timestamp_unix": 1744376653, "type": "comment", "text": "@TomHsiung @zaobaosg 精神胜利法也是胜利的一种  能够理解你们的心情", "context": {"type": "tweet", "id": "1910891690912555294", "text": "整個問題的癥結就在於:\n紙老虎不要給你紙老虎的錢,\n你對紙老虎的錢, 卻愛不釋手。", "author_id": "633906305", "author_username": "dow<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 49}}, {"conversation_id": "1910637768067473830", "tweet_id": "1910738920503157031", "timestamp": "2025-04-11T16:57:13+00:00", "timestamp_unix": 1744390633, "type": "comment", "text": "@Leedani7 @SpoxCHN_LinJian 你的理解能力太弱", "context": {"type": "tweet", "id": "1910700135291507182", "text": "@SpoxCHN_LinJian 🤣🤣🤣🤣🤣🤣\n\nMr <PERSON> or (the CCP) I do not think this weak stance helps the rhetoric that we started off with.\n\nWhat do you mean by “US exports to China are unviable at the current tariff level and thus; the arrogance of <PERSON> will be ignored if he presses further.", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 121}}, {"conversation_id": "1910637768067473830", "tweet_id": "1910838231827882243", "timestamp": "2025-04-11T23:31:50+00:00", "timestamp_unix": 1744414310, "type": "comment", "text": "@Leedani7 @SpoxCHN_LinJian 贸易战了。还开放开放 你去反思一下你的美国爸爸为什么要闭关锁国 为什么要贸易战争", "context": {"type": "tweet", "id": "1910799967436034194", "text": "@liyongkang98553 @SpoxCHN_LinJian 是否存在貿易問題？美國是否表示擔憂？中國是否像其他國家一樣允許其市場開放？", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 41}}, {"conversation_id": "1910637768067473830", "tweet_id": "1910841659337638057", "timestamp": "2025-04-11T23:45:27+00:00", "timestamp_unix": 1744415127, "type": "comment", "text": "@Leedani7 @SpoxCHN_LinJian https://t.co/EDHd43spVr", "context": {"type": "tweet", "id": "1910799967436034194", "text": "@liyongkang98553 @SpoxCHN_LinJian 是否存在貿易問題？美國是否表示擔憂？中國是否像其他國家一樣允許其市場開放？", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1910637768067473830", "tweet_id": "1910997489593057451", "timestamp": "2025-04-12T10:04:40+00:00", "timestamp_unix": 1744452280, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 你不知道 google也在中国运营过吗 只是出于安全考虑数据必须留在国内 他们放弃了，美国为什么要禁止tiktok？你只是 双标狗而已", "context": {"type": "tweet", "id": "1910988479653118354", "text": "@ftdhh22631 @liyongkang98553 @SpoxCHN_LinJian 他們一定認為我們不了解中國的現實。我在中國工作的朋友如果不使用代理和一些軟體就無法使用世界各地正常使用的應用程式。中國的應用程式甚至在中國境外也可以訪問，而來自這個地區的應用程式則被禁止、封鎖。外國公司的技術被中國公司通過，隨後又與中國公司展開競爭，而中國公司當時透過法規和法律允", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1910637768067473830", "tweet_id": "1910998050535035008", "timestamp": "2025-04-12T10:06:54+00:00", "timestamp_unix": 1744452414, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 不好意思，中国并没有被完全殖民过 台湾作为清朝海战失败的后果被日本殖民，其次是香港和澳门。", "context": {"type": "tweet", "id": "1910988529934491664", "text": "@ftdhh22631 @liyongkang98553 @SpoxCHN_LinJian 許外國公司進入中國市場；外國公司的產品變得比中國企業家生產的產品貴。在川普第一任期之前，貿易逆差和市場准入問題就一直存在。中國竊取智慧財產權，並將其竊取和修改的公司財產充斥世界市場，忘記了他們指責西方在與中國打交道時沒有表現出的道德和公平。中國並不是唯一一個被殖民的國家，但卻試圖", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1910637768067473830", "tweet_id": "1910998272665358462", "timestamp": "2025-04-12T10:07:47+00:00", "timestamp_unix": 1744452467, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 假装是个中国通，依然还是那个井底之蛙🐸", "context": {"type": "tweet", "id": "1910988575870423176", "text": "@ftdhh22631 @liyongkang98553 @SpoxCHN_LinJian 搶佔國際市場，而自己的市場卻被守護著。", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1910637768067473830", "tweet_id": "1911006537239408733", "timestamp": "2025-04-12T10:40:37+00:00", "timestamp_unix": 1744454437, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 政府做的不好为什么不能批评 在警察面前都能批评，只要你有证据，台湾和大陆言论自由最大的区别就是台湾可以在没有任何证据情况下造谣抹黑别人而不带来代价，但是你在中国大陆如果造谣抹黑 就可能带来后果，前提是不能伤害别人。别人没有错 为什么要去嘴炮他。再次证明你们被绿色媒体妖魔化中国洗脑", "context": {"type": "tweet", "id": "1911004578482458717", "text": "@liyongkang98553 @ftdhh22631 @SpoxCHN_LinJian 國家之外還要承擔其他目標？現實是，中國人民不能談論反人民、反社會發展的政策，政府決定個人的生活方式……是的，如果知道這一點並反對這一點，讓每個人都以正確的方式評論中共的愚蠢，那麼我/每個人都是井底之蛙；我想知道你屬於哪種類型", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 44}}, {"conversation_id": "1910637768067473830", "tweet_id": "1911006824884847079", "timestamp": "2025-04-12T10:41:46+00:00", "timestamp_unix": 1744454506, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 还是那句话，井底之蛙 这个标签你们绿🐸拿不掉", "context": {"type": "tweet", "id": "1911004578482458717", "text": "@liyongkang98553 @ftdhh22631 @SpoxCHN_LinJian 國家之外還要承擔其他目標？現實是，中國人民不能談論反人民、反社會發展的政策，政府決定個人的生活方式……是的，如果知道這一點並反對這一點，讓每個人都以正確的方式評論中共的愚蠢，那麼我/每個人都是井底之蛙；我想知道你屬於哪種類型", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1910637768067473830", "tweet_id": "1911007608955408642", "timestamp": "2025-04-12T10:44:53+00:00", "timestamp_unix": 1744454693, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 强迫忠诚？你听哪个讲的 中国媒体至少一半以上都是亲西方  你喜欢什么不喜欢什么 谁管你，只要你不危害社会带节奏 你上天都没人管你。再再次证明中国被你们的媒体妖魔化", "context": {"type": "tweet", "id": "1911004533137826035", "text": "@liyongkang98553 @ftdhh22631 @SpoxCHN_LinJian 我當然不是中國人，這是否意味著我不知道中國發生了什麼事？我在這裡有中國朋友，再加上我的其他朋友在中國工作的現實，你不能告訴我他們討厭中國而不得不歪曲現實。你們的政府是否強迫人民忠誠？如果海外公民不合作，你們的政府是否會用他們在中國的家人來威脅他們？中國共產黨是否要求移民除了移居新", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1910637768067473830", "tweet_id": "1911014327580311569", "timestamp": "2025-04-12T11:11:35+00:00", "timestamp_unix": 1744456295, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 中国大陆: 我过得很好  \n台湾人: 不，你过得不好 \n🤡  台湾1450网军给你们洗脑 还挺有效果", "context": {"type": "tweet", "id": "1911011876231688438", "text": "@liyongkang98553 @ftdhh22631 @SpoxCHN_LinJian 你是一個可笑的垃圾，但你甚至不知道這一點。你們的政府其實是強迫忠誠，威脅海外華人歸附，否則他們的家人就會處於危險之中。你們這隻自由的鳳凰無法控制自己的生活，你們有自由意志選擇遵守或反對政府政策。當政府不聽取或考慮其統治人民的選擇，而中共管轄範圍之外的人卻說著中共應該說的話時,", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1910637768067473830", "tweet_id": "1911014662168301985", "timestamp": "2025-04-12T11:12:55+00:00", "timestamp_unix": 1744456375, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 中国反贼这么多，竟然还说中国没有言论自由 不能反政府 不能这个不能那个  🤡 。不，你们只是智力低！", "context": {"type": "tweet", "id": "1911011909152780470", "text": "@liyongkang98553 @ftdhh22631 @SpoxCHN_LinJian 你們這種人無法抗議你們的不滿。與其試圖推銷「生活不關乎我或那些講述生活的人」這一觀點，為什麼不向我們這些反對者證明，我們的中國朋友和我們在中國的朋友是在無緣無故地反對這個出色的政府和他們的治理風格，而不是努力洗清世界所知的現實。", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1910637768067473830", "tweet_id": "1911015558495220051", "timestamp": "2025-04-12T11:16:28+00:00", "timestamp_unix": 1744456588, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 🤡https://t.co/lsie54MbZg", "context": {"type": "tweet", "id": "1911011909152780470", "text": "@liyongkang98553 @ftdhh22631 @SpoxCHN_LinJian 你們這種人無法抗議你們的不滿。與其試圖推銷「生活不關乎我或那些講述生活的人」這一觀點，為什麼不向我們這些反對者證明，我們的中國朋友和我們在中國的朋友是在無緣無故地反對這個出色的政府和他們的治理風格，而不是努力洗清世界所知的現實。", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1910637768067473830", "tweet_id": "1911015960330248396", "timestamp": "2025-04-12T11:18:04+00:00", "timestamp_unix": 1744456684, "type": "comment", "text": "@Leedani7 @ftdhh22631 @SpoxCHN_LinJian 🤡https://t.co/uaFIB9uRm5", "context": {"type": "tweet", "id": "1911011909152780470", "text": "@liyongkang98553 @ftdhh22631 @SpoxCHN_LinJian 你們這種人無法抗議你們的不滿。與其試圖推銷「生活不關乎我或那些講述生活的人」這一觀點，為什麼不向我們這些反對者證明，我們的中國朋友和我們在中國的朋友是在無緣無故地反對這個出色的政府和他們的治理風格，而不是努力洗清世界所知的現實。", "author_id": "4150697999", "author_username": "Leedani7"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1912060960224719136", "tweet_id": "1912163137593462837", "timestamp": "2025-04-15T15:16:32+00:00", "timestamp_unix": 1744730192, "type": "comment", "text": "@JackyHu78760318 @zaobaosg 做人留一线，日后好相见，就是这个道理 毕竟仗打完了 还要继续做生意", "context": {"type": "tweet", "id": "1912068980707410390", "text": "@zaobaosg 中国脱钩了，为何还要留着美国国债呢？ 有人知道这么做的好处么？", "author_id": "1249940091662893057", "author_username": "JackyHu78760318"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 64}}, {"conversation_id": "1914150967697621057", "tweet_id": "1914287416405348559", "timestamp": "2025-04-21T11:57:40+00:00", "timestamp_unix": 1745236660, "type": "comment", "text": "@WiiCht48573 @bbcchinese 毫无疑问，平价gdp中国大陆在十年前就超过美国世界第一了  台湾人就是酸 毕竟只是一个岛", "context": {"type": "tweet", "id": "1914282522885980589", "text": "@bbcchinese 蘋果跑去印度\n俎國 謠謠領先\n😂🤣😂🤣😂", "author_id": "1681217096762662912", "author_username": "WiiCht48573"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 195}}, {"conversation_id": "1915749914467795081", "tweet_id": "1915751935866786252", "timestamp": "2025-04-25T12:57:09+00:00", "timestamp_unix": 1745585829, "type": "comment", "text": "@RKGold @GlobeEyeNews 谁在乎呢，苹果在中国的份额已经只有14％了，以后会更少", "context": {"type": "tweet", "id": "1915751084708049067", "text": "@GlobeEyeNews No worries, Apple is moving production out of China. China should come in and make a deal before other big names leave for better tariff structures.", "author_id": "214326367", "author_username": "RKGold"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 7, "quote_count": 0, "view_count": 538}}]}