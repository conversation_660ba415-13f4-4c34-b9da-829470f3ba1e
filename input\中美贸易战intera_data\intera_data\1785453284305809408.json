{"user_id": "1785453284305809408", "interactions_count": 26, "interactions": [{"conversation_id": "1906422597979603102", "tweet_id": "1906472986640822274", "timestamp": "2025-03-30T22:25:55+00:00", "timestamp_unix": 1743373555, "type": "quote", "text": "Very true\n\nAlso, threat to invade Canada and Greenland", "context": {"type": "tweet", "id": "1906422597979603102", "text": "<PERSON> is the king of threats.\n\nBomb Iran. Tariff Russia.\n\nBut the threats expose a weakness:\n\nThe U.S. political class has nothing to offer but war and more war.\n\nThis is why the world is moving multipolar and the Global South is choosing Russia, China and BRICS.\n\nEvery US war, every act of bullying only isolates the US further.", "author_id": "2300716447", "author_username": "GeopoliticsDH"}, "metrics": {"retweet_count": 1, "reply_count": 5, "like_count": 1, "quote_count": 0, "view_count": 83}}, {"conversation_id": "1906422597979603102", "tweet_id": "1906473066064171310", "timestamp": "2025-03-30T22:26:14+00:00", "timestamp_unix": 1743373574, "type": "comment", "text": "@GeopoliticsDH 💯", "context": {"type": "tweet", "id": "1907933163701285217", "text": "https://t.co/mg7c2I8MK2", "author_id": "1640321000427462662", "author_username": "Rwslan1169935"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 183}}, {"conversation_id": "1906422597979603102", "tweet_id": "1906473789011132644", "timestamp": "2025-03-30T22:29:06+00:00", "timestamp_unix": 1743373746, "type": "comment", "text": "@ChloeKittieKat @GeopoliticsDH What?!\nSimple. Stop funding and supplying weapons to Israel and Ukraine.\nFurther, stop starting wars and try dimplomacy. https://t.co/w7rFjK5btq", "context": {"type": "tweet", "id": "1906427202331132207", "text": "@GeopoliticsDH Maybe you can tell us how to resolve the Middle East or the Ukraine war. Let's hear your proposals.", "author_id": "1358591479543787529", "author_username": "ChloeKittie<PERSON>at"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 49}}, {"conversation_id": "1910022109994098958", "tweet_id": "1910276336507294139", "timestamp": "2025-04-10T10:19:04+00:00", "timestamp_unix": 1744280344, "type": "comment", "text": "@realDonParody US has zero respect for the World’s Markets.\nThese tariffs were imposed by the US on all countries with no regard for WTO Rules.\nThey then lifted them after <PERSON>’s oligarch friends made $billions on the markets.\nUS is a lawless barbarian.", "context": {"type": "tweet", "id": "1910529068992241764", "text": "https://t.co/VXPpUugy4U", "author_id": "1242512444913786881", "author_username": "StromDeb<PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 59}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910423245003849763", "timestamp": "2025-04-10T20:02:50+00:00", "timestamp_unix": 1744315370, "type": "comment", "text": "@trump_repost LOL\nChina’s RETALIATORY tariffs are against the US Market, the culprit that started this trade war, and not the World’s Markets.\nDoes Washington ever tell the truth?", "context": {"type": "tweet", "id": "1914556900009451805", "text": "@deapthoughts @Noahpinion Weird!! He did pause here!", "author_id": "1494812868948152321", "author_username": "MetacriticCap"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 104}}, {"conversation_id": "1911346118266622030", "tweet_id": "1911362524035109349", "timestamp": "2025-04-13T10:15:11+00:00", "timestamp_unix": 1744539311, "type": "comment", "text": "@__<PERSON><PERSON> @MarioNawfal He can throw a tantrum like he usually does but he ain’t got the cards 🤣 https://t.co/9XY4prhqPL", "context": {"type": "tweet", "id": "1911348723386585371", "text": "@MarioNawfal I will be curious to see how <PERSON> reacts to anyone teaming up with China.", "author_id": "1856865982301802496", "author_username": "__<PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 77}}, {"conversation_id": "1911406897019633757", "tweet_id": "1911513418995290519", "timestamp": "2025-04-13T20:14:47+00:00", "timestamp_unix": 1744575287, "type": "comment", "text": "@vlad<PERSON><PERSON><PERSON><PERSON>u No need.", "context": {"type": "tweet", "id": "1912329336700469588", "text": "I don’t understand what tariffs and trades are to be honest I don’t understand I just hope for peace within the world between ALL countries that’s my wish", "author_id": "811643344808005632", "author_username": "duchess<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1911504287508955356", "tweet_id": "1911619932816908714", "timestamp": "2025-04-14T03:18:02+00:00", "timestamp_unix": 1744600682, "type": "comment", "text": "@trump_repost OMG you’re such a cry baby. You will destroy the US economy just because your ego has been bruised.\nYou’re an old man throwing a baby tantrum. https://t.co/OB1EXhlsSg", "context": {"type": "tweet", "id": "1912020531504922837", "text": "Tough stuff to deal with, but it's about time we start standing up for ourselves. China definitely needs a reality check!", "author_id": "888808578", "author_username": "prof_gago"}, "metrics": {"retweet_count": 2, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1912368318432075915", "tweet_id": "1912446130128122195", "timestamp": "2025-04-16T10:01:03+00:00", "timestamp_unix": 1744797663, "type": "comment", "text": "@the<PERSON><PERSON><PERSON><PERSON> Did <PERSON> actually expected them to kiss his ass?", "context": {"type": "tweet", "id": "1912934448481575284", "text": "川普无论怎么做，都会让中国再次伟大。", "author_id": "886808003579568128", "author_username": "hsd8899"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1912443953434992686", "tweet_id": "1912625765491130504", "timestamp": "2025-04-16T21:54:52+00:00", "timestamp_unix": 1744840492, "type": "comment", "text": "@Investingcom Tariffs above 100% are pointless.\nChina doesn’t care and no amount of schoolgirl antics will make Xi call Trump.\nChina has moved on.\nChina has replaced US soybeans, corn, beef, energy and Boeing. They’ve boycotted sale of rare earth and magnets to the US. https://t.co/4INEej6dAt", "context": {"type": "tweet", "id": "1912630236186804717", "text": "https://t.co/yQw1xRV6yx", "author_id": "800490260", "author_username": "acudecontodo"}, "metrics": {"retweet_count": 1, "reply_count": 4, "like_count": 2, "quote_count": 1, "view_count": 106}}, {"conversation_id": "1915612186258661492", "tweet_id": "1915684954643833339", "timestamp": "2025-04-25T08:30:59+00:00", "timestamp_unix": 1745569859, "type": "comment", "text": "@gurgavin FAKE NEWS\nWhere’s your source, bootlicker?", "context": {"type": "tweet", "id": "1915703262696689808", "text": "😲", "author_id": "3550439296", "author_username": "independantnel"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1919133566551617692", "tweet_id": "1919148271324520892", "timestamp": "2025-05-04T21:52:58+00:00", "timestamp_unix": 1746395578, "type": "comment", "text": "@GordonGChang China is winning Trump’s trade war https://t.co/OqZIrdGm32", "context": {"type": "tweet", "id": "1919445505366528449", "text": "This dude has been consistently wrong on China for years. Guess he hasn't learned.", "author_id": "21539304", "author_username": "_ChicagoV_"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 4, "quote_count": 0, "view_count": 465}}, {"conversation_id": "1919342743161037177", "tweet_id": "1919347156038685147", "timestamp": "2025-05-05T11:03:16+00:00", "timestamp_unix": 1746442996, "type": "comment", "text": "@GordonGChang US has lost Trump’s tariff war BIGLY!\n\nChina’s GDP growth last qtr was 5.4%, whilst US’s was -0.3%. That’s NEGATIVE growth!\n\n<PERSON><PERSON><PERSON><PERSON>ha https://t.co/8djo5fDAsT", "context": {"type": "tweet", "id": "1919632601624777155", "text": "맞죠\n계속 짱깨들 몰아부쳐야 합니다\n중국공산당이 해체될 때까지", "author_id": "1519653410844594177", "author_username": "HJang89152754"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 13, "quote_count": 0, "view_count": 361}}, {"conversation_id": "1919342743161037177", "tweet_id": "1919498262408524131", "timestamp": "2025-05-05T21:03:42+00:00", "timestamp_unix": 1746479022, "type": "comment", "text": "@Tim6992 @GordonGChang Can you provide evidence?", "context": {"type": "tweet", "id": "1919497427238711349", "text": "@network89712 @GordonGChang 5.4% is Bs.", "author_id": "1404932099870760961", "author_username": "Tim6992"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1919342743161037177", "tweet_id": "1919509888621019551", "timestamp": "2025-05-05T21:49:54+00:00", "timestamp_unix": 1746481794, "type": "comment", "text": "@Tim6992 @<PERSON><PERSON><PERSON>, it’s all over the news.\nNow, your turn. https://t.co/L3GeXVcheb", "context": {"type": "tweet", "id": "1919500434684694646", "text": "@network89712 @GordonGChang Only if you can.", "author_id": "1404932099870760961", "author_username": "Tim6992"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1919342743161037177", "tweet_id": "1919513461291164038", "timestamp": "2025-05-05T22:04:06+00:00", "timestamp_unix": 1746482646, "type": "comment", "text": "@Tim6992 @Gordon<PERSON><PERSON>, so give me the American version of reliable news and prove me wrong", "context": {"type": "tweet", "id": "1919511761373298817", "text": "@network89712 @GordonGChang Fake news is all over the news last I checked.", "author_id": "1404932099870760961", "author_username": "Tim6992"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1919342743161037177", "tweet_id": "1919525332555559005", "timestamp": "2025-05-05T22:51:16+00:00", "timestamp_unix": 1746485476, "type": "comment", "text": "@Tim6992 @GordonGChang You’re avoiding the issue.", "context": {"type": "tweet", "id": "1919516479059271982", "text": "@network89712 @GordonGChang Certainly isn't CNN. Thanks for playing.", "author_id": "1404932099870760961", "author_username": "Tim6992"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1919342743161037177", "tweet_id": "1919540395677045137", "timestamp": "2025-05-05T23:51:08+00:00", "timestamp_unix": 1746489068, "type": "comment", "text": "@ahfirncw @Tim6992 @GordonGChang <PERSON>ha\nWhere's your source?\nHomemade Excel spreadsheet? 5 y.o. could have done a better job.", "context": {"type": "tweet", "id": "1919632601624777155", "text": "맞죠\n계속 짱깨들 몰아부쳐야 합니다\n중국공산당이 해체될 때까지", "author_id": "1519653410844594177", "author_username": "HJang89152754"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1919342743161037177", "tweet_id": "1919548330704552319", "timestamp": "2025-05-06T00:22:40+00:00", "timestamp_unix": 1746490960, "type": "comment", "text": "@ahfirncw @Tim6992 @GordonGChang Hahahahahahahahahaha\n\nG20 includes Brazil, China, India, Russia, South Africa and Indonesia. All except Indonesia are the original members of BRICS.\n\nYou're sooooooo fkn STUPID!\n\nHahahahahahaha\n\nHahahahahahah\n\nhahahah\n\nhahahahahahahah\n\nhahahaha\n\nhahaha\n\nhahahaha https://t.co/6GPu3JG2tJ", "context": {"type": "tweet", "id": "1919632601624777155", "text": "맞죠\n계속 짱깨들 몰아부쳐야 합니다\n중국공산당이 해체될 때까지", "author_id": "1519653410844594177", "author_username": "HJang89152754"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1919893006267605431", "tweet_id": "1919934510264705479", "timestamp": "2025-05-07T01:57:12+00:00", "timestamp_unix": 1746583032, "type": "comment", "text": "@IngrahamAngle @SecScottBessent OMG, you can't even get a simple fact right.\n\nChina's retaliatory tariff on US is 125%. US's tariff on China is 145%.", "context": {"type": "tweet", "id": "1920142796146487498", "text": "@co3blue @SenRandPaul @POTUS https://t.co/QNhrmm8Faz", "author_id": "1515012649574039552", "author_username": "jebrown9375"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 66}}, {"conversation_id": "1921232868442865772", "tweet_id": "1921358701681619079", "timestamp": "2025-05-11T00:16:26+00:00", "timestamp_unix": 1746922586, "type": "comment", "text": "@AndieLau69 @GordonGChang CHINA BUYS:\nTesla, GM, Ford\niPhones\nMcDonalds, KFC and Starbucks\nBoeing\nSorghum, soybean, corn, beef and pork\n\nUS BANNED:\nAll Chinese EVs\nLuckin\nComac\nHuawei\n\nUS BANNED SALE OF:\nASML\nNVIDIA\n\nUS has <PERSON><PERSON> Syndrome", "context": {"type": "tweet", "id": "1921236611242561865", "text": "@GordonGChang Spot on! It is not a Tarriff War ! It is to corner the CCP to do the right thing! Open up the bloody market CCP", "author_id": "68927460", "author_username": "AndieLau69"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1921232868442865772", "tweet_id": "1921358987586330930", "timestamp": "2025-05-11T00:17:34+00:00", "timestamp_unix": 1746922654, "type": "comment", "text": "@GordonGChang LMFAO\n\nTrump started the trade war in 2018.\n\nCHINA BUYS:\nTesla, GM, Ford\niPhones\nMcDonalds, KFC and Starbucks\nBoeing\nSorghum, soybean, corn, beef and pork\n\nUS BANNED:\nAll Chinese EVs\nLuckin\nComac\nHuawei\n\nUS BANNED SALE OF:\nASML\nNVIDIA\n\nUS has Tonya Harding Syndrome", "context": {"type": "tweet", "id": "1921842354480177231", "text": "China Xi has a peasant mindset and you hear constantly it’s all about saving face and pride.  Chinese Xi has a big, pride chin just like <PERSON>. Intelligent negotiators know how to easily work the “pride” component. German <PERSON><PERSON><PERSON> worked <PERSON> daily", "author_id": "1501294753232662539", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 53}}, {"conversation_id": "1921410177238024278", "tweet_id": "1921471180260696363", "timestamp": "2025-05-11T07:43:23+00:00", "timestamp_unix": 1746949403, "type": "comment", "text": "@StevenWMosher US cannot be trusted.\n<PERSON> started the Trade War with China in 2018. He reneged.\n<PERSON> replaced NAFTA with USMCA. He reneged.\n<PERSON> pulled out of the Paris Climate Accord and Iran nuclear deal.\nUS started countless wars by fabricating lies and false flags.", "context": {"type": "tweet", "id": "1921914310663524471", "text": "中国愿意采取一切措施来取消关税。\n欺骗大师们会承诺一切，但什么也不会实现。\n还记得特朗普与中国达成的 45 项关税协议吗？", "author_id": "1472082973180809219", "author_username": "von20_max"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 98}}, {"conversation_id": "1921881003594781139", "tweet_id": "1922034699100094826", "timestamp": "2025-05-12T21:02:36+00:00", "timestamp_unix": 1747083756, "type": "comment", "text": "@muslimwife1992 @<PERSON><PERSON><PERSON> lives in an alternative universe.\n\nHe has no regard for reality in this universe.", "context": {"type": "tweet", "id": "1921922057194877146", "text": "@GordonGChang UncleChang is fakenews\nMost if NOT ALL major US brands of GOODS &amp; SERVICES are established in China\n\nhttps://t.co/IN8tgIMoEL https://t.co/6qB5yRVX6U", "author_id": "1796769091237367808", "author_username": "muslimwife1992"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921891799884321186", "tweet_id": "1922109436513968455", "timestamp": "2025-05-13T01:59:35+00:00", "timestamp_unix": 1747101575, "type": "comment", "text": "@CarlZha https://t.co/9PTa4Y1dSN", "context": {"type": "tweet", "id": "1922379540552204683", "text": "@atrupar Trump is weaker than ever, haha. Chinese people are calling <PERSON> a \"dog of <PERSON>\" and saying <PERSON> has helped China by protecting Pakistan, as its investments and trade routes were in danger\nhttps://t.co/YMEC1uN1EP", "author_id": "1879908966395731968", "author_username": "p<PERSON><PERSON><PERSON>son1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1922762599340397054", "tweet_id": "1922782756204876134", "timestamp": "2025-05-14T22:35:07+00:00", "timestamp_unix": 1747262107, "type": "comment", "text": "@GordonGChang @_brittany<PERSON><PERSON> @Forbes Ummm this was <PERSON>’s trade war and the US lost.\n\nThe world saw <PERSON> folding like an origami dog 🐶 \n\nhttps://t.co/9PTa4Y1dSN", "context": {"type": "tweet", "id": "1922762599340397054", "text": "原始推文内容不可用", "author_id": "151930383", "author_username": "<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 4, "quote_count": 0, "view_count": 136}}]}