import json
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from collections import defaultdict, Counter
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DataAnalyzer:
    def __init__(self, input_dir="input", output_dir="output"):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.input_data = {}
        self.output_data = {}
        
    def load_input_data(self):
        """加载输入数据"""
        print("正在加载输入数据...")
        
        for topic_dir in os.listdir(self.input_dir):
            if not os.path.isdir(os.path.join(self.input_dir, topic_dir)):
                continue
                
            topic_name = topic_dir.replace("_intera_data", "")
            self.input_data[topic_name] = []
            
            intera_data_path = os.path.join(self.input_dir, topic_dir, "intera_data")
            if not os.path.exists(intera_data_path):
                continue
                
            for file_name in os.listdir(intera_data_path):
                if file_name.endswith('.json'):
                    file_path = os.path.join(intera_data_path, file_name)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            self.input_data[topic_name].append(data)
                    except Exception as e:
                        print(f"读取文件 {file_path} 时出错: {e}")
        
        print(f"输入数据加载完成，共 {len(self.input_data)} 个主题")
        
    def load_output_data(self):
        """加载输出数据"""
        print("正在加载输出数据...")
        
        for topic_dir in os.listdir(self.output_dir):
            if not os.path.isdir(os.path.join(self.output_dir, topic_dir)):
                continue
                
            topic_name = topic_dir.replace("_output", "")
            self.output_data[topic_name] = []
            
            topic_path = os.path.join(self.output_dir, topic_dir)
            
            for user_dir in os.listdir(topic_path):
                user_path = os.path.join(topic_path, user_dir)
                if not os.path.isdir(user_path):
                    continue
                    
                for file_name in os.listdir(user_path):
                    if file_name.endswith('.json'):
                        file_path = os.path.join(user_path, file_name)
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                self.output_data[topic_name].append(data)
                        except Exception as e:
                            print(f"读取文件 {file_path} 时出错: {e}")
        
        print(f"输出数据加载完成，共 {len(self.output_data)} 个主题")
    
    def analyze_input_data(self):
        """分析输入数据"""
        print("正在分析输入数据...")
        
        input_stats = {}
        
        for topic, data_list in self.input_data.items():
            stats = {
                'total_users': len(data_list),
                'total_interactions': 0,
                'interaction_types': Counter(),
                'metrics_stats': defaultdict(list),
                'interactions_per_user': [],
                'timestamps': []
            }
            
            for user_data in data_list:
                interactions_count = user_data.get('interactions_count', 0)
                stats['total_interactions'] += interactions_count
                stats['interactions_per_user'].append(interactions_count)
                
                for interaction in user_data.get('interactions', []):
                    # 交互类型统计
                    interaction_type = interaction.get('type', 'unknown')
                    stats['interaction_types'][interaction_type] += 1
                    
                    # 指标统计
                    metrics = interaction.get('metrics', {})
                    for metric, value in metrics.items():
                        if isinstance(value, (int, float)):
                            stats['metrics_stats'][metric].append(value)
                    
                    # 时间戳统计
                    timestamp = interaction.get('timestamp_unix')
                    if timestamp:
                        stats['timestamps'].append(timestamp)
            
            input_stats[topic] = stats
        
        return input_stats
    
    def analyze_output_data(self):
        """分析输出数据"""
        print("正在分析输出数据...")
        
        output_stats = {}
        
        for topic, data_list in self.output_data.items():
            stats = {
                'total_analyses': len(data_list),
                'debate_rounds': [],
                'consensus_reached': [],
                'confidence_scores': [],
                'intent_categories': Counter(),
                'behavior_types': Counter(),
                'emotional_tendencies': Counter(),
                'evaluation_scores': defaultdict(list)
            }
            
            for analysis in data_list:
                # 辩论轮数
                debate_rounds = analysis.get('debate_rounds', 0)
                stats['debate_rounds'].append(debate_rounds)
                
                # 是否达成共识
                consensus = analysis.get('consensus_reached', False)
                stats['consensus_reached'].append(consensus)
                
                # 置信度分数
                final_label = analysis.get('final_structured_intent_label', {})
                confidence = final_label.get('confidence_score', 0)
                stats['confidence_scores'].append(confidence)
                
                # 意图类别
                intent_category = final_label.get('coarse_intent_category', 'unknown')
                stats['intent_categories'][intent_category] += 1
                
                # 行为类型
                behavior_set = final_label.get('behavior_set', [])
                for behavior in behavior_set:
                    behavior_type = behavior.get('behavior_type', 'unknown')
                    stats['behavior_types'][behavior_type] += 1
                
                # 情感倾向
                context_analysis = analysis.get('context_analysis', {}).get('analysis', {})
                emotional_tendency = context_analysis.get('emotional_tendency', {})
                overall_sentiment = emotional_tendency.get('overall_sentiment', 'unknown')
                stats['emotional_tendencies'][overall_sentiment] += 1
                
                # 评估分数
                senior_eval = analysis.get('senior_evaluation', {}).get('evaluation', {})
                eval_results = senior_eval.get('evaluation_results', [])
                for result in eval_results:
                    scores = result.get('scores', {})
                    for metric, score in scores.items():
                        stats['evaluation_scores'][metric].append(score)
            
            output_stats[topic] = stats
        
        return output_stats
    
    def create_visualizations(self, input_stats, output_stats):
        """创建可视化图表"""
        print("正在创建可视化图表...")
        
        # 创建图表目录
        os.makedirs('charts', exist_ok=True)
        
        # 1. 输入数据概览
        self.plot_input_overview(input_stats)
        
        # 2. 交互类型分布
        self.plot_interaction_types(input_stats)
        
        # 3. 用户交互数量分布
        self.plot_interactions_per_user(input_stats)
        
        # 4. 社交媒体指标分布
        self.plot_social_metrics(input_stats)
        
        # 5. 输出数据概览
        self.plot_output_overview(output_stats)
        
        # 6. 意图分类分布
        self.plot_intent_categories(output_stats)
        
        # 7. 情感倾向分布
        self.plot_emotional_tendencies(output_stats)
        
        # 8. 评估分数分布
        self.plot_evaluation_scores(output_stats)
        
        # 9. 置信度分布
        self.plot_confidence_scores(output_stats)
        
        # 10. 辩论轮数分布
        self.plot_debate_rounds(output_stats)
        
        print("图表创建完成，保存在 charts/ 目录下")
    
    def plot_input_overview(self, input_stats):
        """绘制输入数据概览"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('输入数据概览', fontsize=16, fontweight='bold')
        
        topics = list(input_stats.keys())
        
        # 用户数量
        user_counts = [input_stats[topic]['total_users'] for topic in topics]
        axes[0, 0].bar(topics, user_counts, color='skyblue')
        axes[0, 0].set_title('各主题用户数量')
        axes[0, 0].set_ylabel('用户数量')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 总交互数量
        interaction_counts = [input_stats[topic]['total_interactions'] for topic in topics]
        axes[0, 1].bar(topics, interaction_counts, color='lightgreen')
        axes[0, 1].set_title('各主题总交互数量')
        axes[0, 1].set_ylabel('交互数量')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 平均每用户交互数
        avg_interactions = [interaction_counts[i] / user_counts[i] if user_counts[i] > 0 else 0 
                          for i in range(len(topics))]
        axes[1, 0].bar(topics, avg_interactions, color='orange')
        axes[1, 0].set_title('平均每用户交互数')
        axes[1, 0].set_ylabel('平均交互数')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 数据完整性
        axes[1, 1].pie([sum(user_counts)], labels=['总用户数'], autopct='%1.0f', startangle=90)
        axes[1, 1].set_title(f'总用户数: {sum(user_counts)}')
        
        plt.tight_layout()
        plt.savefig('charts/input_overview.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_interaction_types(self, input_stats):
        """绘制交互类型分布"""
        fig, axes = plt.subplots(1, len(input_stats), figsize=(5*len(input_stats), 6))
        if len(input_stats) == 1:
            axes = [axes]

        fig.suptitle('各主题交互类型分布', fontsize=16, fontweight='bold')

        for i, (topic, stats) in enumerate(input_stats.items()):
            interaction_types = stats['interaction_types']
            if interaction_types:
                types = list(interaction_types.keys())
                counts = list(interaction_types.values())

                axes[i].pie(counts, labels=types, autopct='%1.1f%%', startangle=90)
                axes[i].set_title(f'{topic}\n总交互: {sum(counts)}')
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{topic}\n无数据')

        plt.tight_layout()
        plt.savefig('charts/interaction_types.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_interactions_per_user(self, input_stats):
        """绘制用户交互数量分布"""
        fig, axes = plt.subplots(1, len(input_stats), figsize=(5*len(input_stats), 6))
        if len(input_stats) == 1:
            axes = [axes]

        fig.suptitle('用户交互数量分布', fontsize=16, fontweight='bold')

        for i, (topic, stats) in enumerate(input_stats.items()):
            interactions_per_user = stats['interactions_per_user']
            if interactions_per_user:
                axes[i].hist(interactions_per_user, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
                axes[i].set_title(f'{topic}\n平均: {np.mean(interactions_per_user):.1f}')
                axes[i].set_xlabel('每用户交互数')
                axes[i].set_ylabel('用户数量')
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{topic}\n无数据')

        plt.tight_layout()
        plt.savefig('charts/interactions_per_user.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_social_metrics(self, input_stats):
        """绘制社交媒体指标分布"""
        metrics_to_plot = ['like_count', 'retweet_count', 'reply_count', 'view_count']

        for metric in metrics_to_plot:
            fig, axes = plt.subplots(1, len(input_stats), figsize=(5*len(input_stats), 6))
            if len(input_stats) == 1:
                axes = [axes]

            fig.suptitle(f'{metric.replace("_", " ").title()} 分布', fontsize=16, fontweight='bold')

            for i, (topic, stats) in enumerate(input_stats.items()):
                metric_values = stats['metrics_stats'].get(metric, [])
                if metric_values:
                    # 过滤异常值
                    metric_values = [v for v in metric_values if v >= 0]
                    if metric_values:
                        axes[i].hist(metric_values, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
                        axes[i].set_title(f'{topic}\n平均: {np.mean(metric_values):.1f}')
                        axes[i].set_xlabel(metric.replace('_', ' ').title())
                        axes[i].set_ylabel('频次')
                        axes[i].set_yscale('log')  # 使用对数刻度
                    else:
                        axes[i].text(0.5, 0.5, '无有效数据', ha='center', va='center', transform=axes[i].transAxes)
                        axes[i].set_title(f'{topic}\n无有效数据')
                else:
                    axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                    axes[i].set_title(f'{topic}\n无数据')

            plt.tight_layout()
            plt.savefig(f'charts/{metric}_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()

    def plot_output_overview(self, output_stats):
        """绘制输出数据概览"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('输出数据概览', fontsize=16, fontweight='bold')

        topics = list(output_stats.keys())

        # 分析数量
        analysis_counts = [output_stats[topic]['total_analyses'] for topic in topics]
        axes[0, 0].bar(topics, analysis_counts, color='lightcoral')
        axes[0, 0].set_title('各主题分析数量')
        axes[0, 0].set_ylabel('分析数量')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # 平均辩论轮数
        avg_debate_rounds = [np.mean(output_stats[topic]['debate_rounds']) if output_stats[topic]['debate_rounds'] else 0
                           for topic in topics]
        axes[0, 1].bar(topics, avg_debate_rounds, color='gold')
        axes[0, 1].set_title('平均辩论轮数')
        axes[0, 1].set_ylabel('轮数')
        axes[0, 1].tick_params(axis='x', rotation=45)

        # 共识达成率
        consensus_rates = [np.mean(output_stats[topic]['consensus_reached']) if output_stats[topic]['consensus_reached'] else 0
                         for topic in topics]
        axes[1, 0].bar(topics, consensus_rates, color='lightblue')
        axes[1, 0].set_title('共识达成率')
        axes[1, 0].set_ylabel('比例')
        axes[1, 0].set_ylim(0, 1)
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 平均置信度
        avg_confidence = [np.mean(output_stats[topic]['confidence_scores']) if output_stats[topic]['confidence_scores'] else 0
                        for topic in topics]
        axes[1, 1].bar(topics, avg_confidence, color='mediumpurple')
        axes[1, 1].set_title('平均置信度')
        axes[1, 1].set_ylabel('置信度')
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig('charts/output_overview.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_intent_categories(self, output_stats):
        """绘制意图分类分布"""
        fig, axes = plt.subplots(1, len(output_stats), figsize=(5*len(output_stats), 6))
        if len(output_stats) == 1:
            axes = [axes]

        fig.suptitle('意图分类分布', fontsize=16, fontweight='bold')

        for i, (topic, stats) in enumerate(output_stats.items()):
            intent_categories = stats['intent_categories']
            if intent_categories:
                categories = list(intent_categories.keys())
                counts = list(intent_categories.values())

                axes[i].pie(counts, labels=categories, autopct='%1.1f%%', startangle=90)
                axes[i].set_title(f'{topic}\n总分析: {sum(counts)}')
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{topic}\n无数据')

        plt.tight_layout()
        plt.savefig('charts/intent_categories.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_emotional_tendencies(self, output_stats):
        """绘制情感倾向分布"""
        fig, axes = plt.subplots(1, len(output_stats), figsize=(5*len(output_stats), 6))
        if len(output_stats) == 1:
            axes = [axes]

        fig.suptitle('情感倾向分布', fontsize=16, fontweight='bold')

        for i, (topic, stats) in enumerate(output_stats.items()):
            emotional_tendencies = stats['emotional_tendencies']
            if emotional_tendencies:
                emotions = list(emotional_tendencies.keys())
                counts = list(emotional_tendencies.values())

                colors = ['red' if e == 'negative' else 'green' if e == 'positive' else 'gray' for e in emotions]
                axes[i].bar(emotions, counts, color=colors, alpha=0.7)
                axes[i].set_title(f'{topic}\n总分析: {sum(counts)}')
                axes[i].set_ylabel('数量')
                axes[i].tick_params(axis='x', rotation=45)
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{topic}\n无数据')

        plt.tight_layout()
        plt.savefig('charts/emotional_tendencies.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_evaluation_scores(self, output_stats):
        """绘制评估分数分布"""
        evaluation_metrics = ['evidence_sufficiency', 'specificity_clarity', 'context_consistency',
                            'internal_logic', 'argument_strength']

        fig, axes = plt.subplots(len(evaluation_metrics), len(output_stats),
                               figsize=(5*len(output_stats), 4*len(evaluation_metrics)))
        if len(output_stats) == 1:
            axes = axes.reshape(-1, 1)
        if len(evaluation_metrics) == 1:
            axes = axes.reshape(1, -1)

        fig.suptitle('评估分数分布', fontsize=16, fontweight='bold')

        for i, metric in enumerate(evaluation_metrics):
            for j, (topic, stats) in enumerate(output_stats.items()):
                scores = stats['evaluation_scores'].get(metric, [])
                if scores:
                    axes[i, j].hist(scores, bins=20, alpha=0.7, color='lightblue', edgecolor='black')
                    axes[i, j].set_title(f'{topic} - {metric}\n平均: {np.mean(scores):.3f}')
                    axes[i, j].set_xlabel('分数')
                    axes[i, j].set_ylabel('频次')
                    axes[i, j].set_xlim(0, 1)
                else:
                    axes[i, j].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i, j].transAxes)
                    axes[i, j].set_title(f'{topic} - {metric}\n无数据')

        plt.tight_layout()
        plt.savefig('charts/evaluation_scores.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_confidence_scores(self, output_stats):
        """绘制置信度分布"""
        fig, axes = plt.subplots(1, len(output_stats), figsize=(5*len(output_stats), 6))
        if len(output_stats) == 1:
            axes = [axes]

        fig.suptitle('置信度分布', fontsize=16, fontweight='bold')

        for i, (topic, stats) in enumerate(output_stats.items()):
            confidence_scores = stats['confidence_scores']
            if confidence_scores:
                axes[i].hist(confidence_scores, bins=20, alpha=0.7, color='gold', edgecolor='black')
                axes[i].set_title(f'{topic}\n平均: {np.mean(confidence_scores):.3f}')
                axes[i].set_xlabel('置信度')
                axes[i].set_ylabel('频次')
                axes[i].set_xlim(0, 1)
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{topic}\n无数据')

        plt.tight_layout()
        plt.savefig('charts/confidence_scores.png', dpi=300, bbox_inches='tight')
        plt.close()

    def plot_debate_rounds(self, output_stats):
        """绘制辩论轮数分布"""
        fig, axes = plt.subplots(1, len(output_stats), figsize=(5*len(output_stats), 6))
        if len(output_stats) == 1:
            axes = [axes]

        fig.suptitle('辩论轮数分布', fontsize=16, fontweight='bold')

        for i, (topic, stats) in enumerate(output_stats.items()):
            debate_rounds = stats['debate_rounds']
            if debate_rounds:
                unique_rounds = sorted(set(debate_rounds))
                round_counts = [debate_rounds.count(r) for r in unique_rounds]

                axes[i].bar(unique_rounds, round_counts, alpha=0.7, color='lightcoral')
                axes[i].set_title(f'{topic}\n平均: {np.mean(debate_rounds):.1f}轮')
                axes[i].set_xlabel('辩论轮数')
                axes[i].set_ylabel('频次')
                axes[i].set_xticks(unique_rounds)
            else:
                axes[i].text(0.5, 0.5, '无数据', ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{topic}\n无数据')

        plt.tight_layout()
        plt.savefig('charts/debate_rounds.png', dpi=300, bbox_inches='tight')
        plt.close()

    def generate_summary_report(self, input_stats, output_stats):
        """生成统计摘要报告"""
        print("\n" + "="*50)
        print("数据统计摘要报告")
        print("="*50)

        print("\n【输入数据统计】")
        for topic, stats in input_stats.items():
            print(f"\n主题: {topic}")
            print(f"  用户数量: {stats['total_users']}")
            print(f"  总交互数: {stats['total_interactions']}")
            if stats['interactions_per_user']:
                print(f"  平均每用户交互数: {np.mean(stats['interactions_per_user']):.1f}")

            print(f"  交互类型分布:")
            for itype, count in stats['interaction_types'].most_common():
                print(f"    {itype}: {count}")

            print(f"  社交媒体指标平均值:")
            for metric, values in stats['metrics_stats'].items():
                if values:
                    print(f"    {metric}: {np.mean(values):.1f}")

        print("\n【输出数据统计】")
        for topic, stats in output_stats.items():
            print(f"\n主题: {topic}")
            print(f"  分析数量: {stats['total_analyses']}")
            if stats['debate_rounds']:
                print(f"  平均辩论轮数: {np.mean(stats['debate_rounds']):.1f}")
            if stats['consensus_reached']:
                print(f"  共识达成率: {np.mean(stats['consensus_reached']):.1%}")
            if stats['confidence_scores']:
                print(f"  平均置信度: {np.mean(stats['confidence_scores']):.3f}")

            print(f"  意图分类分布:")
            for category, count in stats['intent_categories'].most_common():
                print(f"    {category}: {count}")

            print(f"  情感倾向分布:")
            for emotion, count in stats['emotional_tendencies'].most_common():
                print(f"    {emotion}: {count}")

        print("\n" + "="*50)

    def run_analysis(self):
        """运行完整分析"""
        # 加载数据
        self.load_input_data()
        self.load_output_data()

        # 分析数据
        input_stats = self.analyze_input_data()
        output_stats = self.analyze_output_data()

        # 创建可视化
        self.create_visualizations(input_stats, output_stats)

        # 生成报告
        self.generate_summary_report(input_stats, output_stats)

        return input_stats, output_stats

if __name__ == "__main__":
    analyzer = DataAnalyzer()
    input_stats, output_stats = analyzer.run_analysis()
