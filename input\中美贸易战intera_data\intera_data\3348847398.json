{"user_id": "3348847398", "interactions_count": 19, "interactions": [{"conversation_id": "1908099500175962361", "tweet_id": "1908115901838197201", "timestamp": "2025-04-04T11:14:16+00:00", "timestamp_unix": 1743765256, "type": "comment", "text": "@zerohedge They import 3x as much to the US.\n\nThere will be some pain since they import $143B from US but they export $438B https://t.co/cRNEBWhykl", "context": {"type": "tweet", "id": "1909934382967570481", "text": "This is $PVP\n\nYOU CAN BUY IT HERE: \n\nhttps://t.co/frni4lVj4L", "author_id": "1808656931042832387", "author_username": "SolNeiviS"}, "metrics": {"retweet_count": 1, "reply_count": 10, "like_count": 23, "quote_count": 0, "view_count": 13057}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908120094460215618", "timestamp": "2025-04-04T11:30:56+00:00", "timestamp_unix": 1743766256, "type": "comment", "text": "@QuietSilver @zerohedge I’m implying that there is a large trade imbalance so tariffs hurt them more than the US\n\nThey can redirect some product but there will definitely be pain felt.", "context": {"type": "tweet", "id": "1908118701628613043", "text": "@FinSavvyMike @zerohedge If you're implying that this is bad for China, remember that exports to the USA is just 2% of their GDP. They can easily redirect much of their current exports for domestic consumption.", "author_id": "544941009", "author_username": "QuietSilver"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 623}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908122723772928289", "timestamp": "2025-04-04T11:41:23+00:00", "timestamp_unix": 1743766883, "type": "comment", "text": "@QuietSilver @zerohedge My assumption was that they were already selling a ton of stuff to Europe.  They can try to increase it by lowering prices (?) but Europenwill have to defend its industries also.  Will they add tariffs to Chinese goods?", "context": {"type": "tweet", "id": "1908122014742917579", "text": "@FinSavvyMike @zerohedge Yes, if they want to sell in the US. They can simply move goods to Europe.", "author_id": "544941009", "author_username": "QuietSilver"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 182}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908123141185863796", "timestamp": "2025-04-04T11:43:02+00:00", "timestamp_unix": 1743766982, "type": "comment", "text": "@JhonnyB17025719 @QuietSilver @zerohedge I agree.  My assumption is that they are already shipping enough product to Europe to meet demand there.  \n\nChina isn’t production constrained so they are meeting demand in the US and Europe right now.", "context": {"type": "tweet", "id": "1908122598996574318", "text": "@QuietSilver @FinSavvyMike @zerohedge that's not how that works, you don't just \"redirect\" that much trade. If there were buyers there, they'd already be selling it there. what, do you think they made it exclusively sold to the USA? they sell to whoever they can at all times.", "author_id": "1482583800995622923", "author_username": "JhonnyB17025719"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 57}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908125789817631023", "timestamp": "2025-04-04T11:53:34+00:00", "timestamp_unix": 1743767614, "type": "comment", "text": "@JhonnyB17025719 @QuietSilver @zerohedge Half a trillion dollars of product being tariffed is huge.  This will hurt", "context": {"type": "tweet", "id": "1908125456005464262", "text": "@QuietSilver @FinSavvyMike @zerohedge you must not realize the astronomical amount of trade US and China does. The scale makes it tough; china doesn't have high domestic consumption; to eat these losses they'd have to take on high amounts of debt like 2008.", "author_id": "1482583800995622923", "author_username": "JhonnyB17025719"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 34}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908130773149884483", "timestamp": "2025-04-04T12:13:22+00:00", "timestamp_unix": 1743768802, "type": "comment", "text": "@7etsuos @zerohedge Well hopefully not.  I believe this is meant as a negotiation tactic", "context": {"type": "tweet", "id": "1908128120747569244", "text": "@FinSavvyMike @zerohedge That means that you americans will pay those extra 35% to those $438B (around extra $153B ) to cover taxes that will go in the pockets of US government 🤣 amazing", "author_id": "1726510318078070784", "author_username": "7etsuos"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 215}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908137218532598123", "timestamp": "2025-04-04T12:38:59+00:00", "timestamp_unix": 1743770339, "type": "comment", "text": "@ArcaneKnowledge @zerohedge I believe this is a huge negotiating tactic in order to reset tariffs", "context": {"type": "tweet", "id": "1908134931634561342", "text": "@FinSavvyMike @zerohedge It's funny that people think these other countries pick up the tariff/tax increase. It's actually passed onto the consumer... https://t.co/R8FdqjYRNw", "author_id": "*********", "author_username": "ArcaneKnowledge"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 490}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908139367601762480", "timestamp": "2025-04-04T12:47:31+00:00", "timestamp_unix": 1743770851, "type": "comment", "text": "@mikelnimrod @zerohedge Lol true, but China’s numbers are suspect as well.", "context": {"type": "tweet", "id": "1909934382967570481", "text": "This is $PVP\n\nYOU CAN BUY IT HERE: \n\nhttps://t.co/frni4lVj4L", "author_id": "1808656931042832387", "author_username": "SolNeiviS"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 154}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908140084936777962", "timestamp": "2025-04-04T12:50:22+00:00", "timestamp_unix": 1743771022, "type": "comment", "text": "@ArcaneKnowledge @zerohedge I hope you’re wrong also, for all our sakes.\n\nI’m hoping this is a short term tactic, because they need the economy back up and running before the Nov 2026 midterm elections.  Otherwise they will lose majority in congress.", "context": {"type": "tweet", "id": "1908139548044935190", "text": "@FinSavvyMike @zerohedge I doubt it, this is an attack on the middle class and the global economy. They want to usher in CBDC, digital ID's, and smart cities. They need to bring it all down first so they can offer the solution. I hope I'm wrong...", "author_id": "*********", "author_username": "ArcaneKnowledge"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 94}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908157851966537895", "timestamp": "2025-04-04T14:00:58+00:00", "timestamp_unix": 1743775258, "type": "comment", "text": "@imabr47 @ArcaneKnowledge @zerohedge Were there ones like that?  I thought it was reciprocal", "context": {"type": "tweet", "id": "1908153815796240664", "text": "@FinSavvyMike @ArcaneKnowledge @zerohedge Then why are we putting a 10% base tariff on countries we had free trade with for decades?", "author_id": "26889027", "author_username": "imabr47"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908158552515993887", "timestamp": "2025-04-04T14:03:45+00:00", "timestamp_unix": 1743775425, "type": "comment", "text": "@ArcaneKnowledge @zerohedge I think it will be like this all summer.  Lots of uncertainty until the tariffs are more settled.\n\nThen the hope is things settle and sentiment goes positive again", "context": {"type": "tweet", "id": "1908144556618465455", "text": "@FinSavvyMike @zerohedge Markets lost trillions yesterday, and they're not looking good today. Forecasters are talking about an increased chance of a recession and more inflation. https://t.co/f2svXhM4Bw", "author_id": "*********", "author_username": "ArcaneKnowledge"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908158605406159293", "timestamp": "2025-04-04T14:03:58+00:00", "timestamp_unix": 1743775438, "type": "comment", "text": "@lthomps54 @zerohedge Yes!", "context": {"type": "tweet", "id": "1908142268835393720", "text": "@FinSavvyMike @zerohedge Companies need to get out of china and mfg here.", "author_id": "1523414748", "author_username": "lthomps54"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 195}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908159353921716526", "timestamp": "2025-04-04T14:06:56+00:00", "timestamp_unix": 1743775616, "type": "comment", "text": "@imabr47 @ArcaneKnowledge @zerohedge Interesting.  Wonder if these will be rolled back as everyone starts negotiating", "context": {"type": "tweet", "id": "1908158812801962025", "text": "@FinSavvyMike @ArcaneKnowledge @zerohedge Just check pretty much every one that has a base 10% tariff. <PERSON> didn't use other countries tariffs as his baseline to create reciprocal tariffs. He used trade deficits to make his calculation. Basically everyone with a trade deficit got a tariff.", "author_id": "26889027", "author_username": "imabr47"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908159960183160994", "timestamp": "2025-04-04T14:09:21+00:00", "timestamp_unix": 1743775761, "type": "comment", "text": "@imabr47 @ArcaneKnowledge @zerohedge These are the problems with a huge sweeping tariff like this.  You create a mess for places where there was no problem to begin with \n\nShould have been more focused on the big players", "context": {"type": "tweet", "id": "1908159441305747678", "text": "@FinSavvyMike @ArcaneKnowledge @zerohedge He also did things like put a 29% tariff on a small island with a population of 2,000 people that export zero goods to the US. Punishing them for simply buying goods from us and not sending anything back. They don't have any resources that we want.", "author_id": "26889027", "author_username": "imabr47"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908167035168723219", "timestamp": "2025-04-04T14:37:27+00:00", "timestamp_unix": 1743777447, "type": "comment", "text": "@imabr47 @ArcaneKnowledge @zerohedge Canada and Mexico weren’t on that list because they got the 25% tariff earlier", "context": {"type": "tweet", "id": "1908162810720235557", "text": "@FinSavvyMike @ArcaneKnowledge @zerohedge I just went and compiled a list of every country that the US has a free trade agreement with and every single one received at least a 10% \"reciprocal\" tariff with two of them receiving higher than 10% (Israel and Nicaragua). Canada and Mexico we're still not sure about. https://t.co/N9x9kB9pfX", "author_id": "26889027", "author_username": "imabr47"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1908099500175962361", "tweet_id": "1908167167318630849", "timestamp": "2025-04-04T14:37:59+00:00", "timestamp_unix": 1743777479, "type": "comment", "text": "@UnMarketingDept @zerohedge Yes but it could put a dent into their economy if they leave this as is", "context": {"type": "tweet", "id": "1908163323272536566", "text": "@FinSavvyMike @zerohedge Yes, but it’s not like we’re their only customer.\n\nhttps://t.co/GDhEzR3Eie", "author_id": "1518350589075329024", "author_username": "UnMarketingDept"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 73}}, {"conversation_id": "1910404819187122193", "tweet_id": "1910414824355377428", "timestamp": "2025-04-10T19:29:22+00:00", "timestamp_unix": 1744313362, "type": "comment", "text": "@tanvi_ratna I read that <PERSON> continued for a third term which ends in 2027.\n\nIf true, and he wants to continue beyond that, he will need to show a positive course for China wouldnt he?\n\nA continued tariff war would weaken his position.", "context": {"type": "tweet", "id": "1911108455701561378", "text": "That's the point! 👇👇👇👇", "author_id": "12734122", "author_username": "<PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 351}}, {"conversation_id": "1920782340827029603", "tweet_id": "1920800628349063407", "timestamp": "2025-05-09T11:18:51+00:00", "timestamp_unix": 1746789531, "type": "quote", "text": "Takeaway from the first tariff negotiation is that every country will get a minimum 10% tariff.\n\n$spy $qqq", "context": {"type": "tweet", "id": "1920782340827029603", "text": "It increasingly looks like the best case scenario on tariffs will be roughly the 60% China and 10% all others, amounting to a roughly 15% hike in overall tariff rates.\n\nWhile there was a lot of hoopla about the UK deal, more than anything it solidified this path.\n\nThread.", "author_id": "1499109588674793476", "author_username": "BobEUnlimited"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 90}}, {"conversation_id": "1920803078174851085", "tweet_id": "1920809990496887206", "timestamp": "2025-05-09T11:56:03+00:00", "timestamp_unix": 1746791763, "type": "comment", "text": "@DeItaone Trump hasn’t moved off the China 145% tariff yet but he did show his hand a little here.\n\nStill, 80% is much much higher than the 10% he just negotiated with the UK.", "context": {"type": "tweet", "id": "1921976888114757908", "text": "@SpencerHakimian Trump's not in charge anymore. Only way to explain a total cave just hours after saying this.\nhttps://t.co/qnQaCUOqIF", "author_id": "770050195136667649", "author_username": "AZinCLE"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 1155}}]}