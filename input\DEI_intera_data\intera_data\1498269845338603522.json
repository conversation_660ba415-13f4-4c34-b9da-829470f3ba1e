{"user_id": "1498269845338603522", "interactions_count": 20, "interactions": [{"conversation_id": "1920075790231781390", "tweet_id": "1920100125684138473", "timestamp": "2025-05-07T12:55:18+00:00", "timestamp_unix": 1746622518, "type": "comment", "text": "@Lorenzo32843904 @Lorellastelle Dobbiamo smettere di dare luce sui socia a sto tipo, facciamo solo il suo gioco.\n\nIndifferenza totale.", "context": {"type": "tweet", "id": "1920463519473971459", "text": "Tanto è tranquillo che non fanno casini sono tutti fermi", "author_id": "1255022747987689473", "author_username": "italia_pro"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 4, "quote_count": 0, "view_count": 106}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921927832239771999", "timestamp": "2025-05-12T13:57:57+00:00", "timestamp_unix": 1747058277, "type": "comment", "text": "@Original_LF @mimmo_rinaldi ma hai letto i primi 4 punti?\no ti sei fatto spiegare i punti dai Leghisti?", "context": {"type": "tweet", "id": "1921902948495294743", "text": "@mimmo_rinaldi Mimmo. I primi 4 quesiti lo mettono letteralmente nel culo degli operai, anche se tu fai fatica ad accorgertene", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921944524521124090", "timestamp": "2025-05-12T15:04:17+00:00", "timestamp_unix": 1747062257, "type": "comment", "text": "@Original_LF @mimmo_rinaldi quello per il sub appalto delle aziende?", "context": {"type": "tweet", "id": "1921940204350222415", "text": "@Ciccioallin @mimmo_r<PERSON>di <PERSON>o, spie<PERSON><PERSON> come lo sai tu, e io ti dico perché lo mette in culo al lavoratore.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921944681627136207", "timestamp": "2025-05-12T15:04:54+00:00", "timestamp_unix": 1747062294, "type": "comment", "text": "@Original_LF @mimmo_rinaldi perchè secondo te non dovrebbero assumersi la responsabilità?", "context": {"type": "tweet", "id": "1921940204350222415", "text": "@Ciccioallin @mimmo_r<PERSON>di <PERSON>o, spie<PERSON><PERSON> come lo sai tu, e io ti dico perché lo mette in culo al lavoratore.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921945375293767861", "timestamp": "2025-05-12T15:07:40+00:00", "timestamp_unix": 1747062460, "type": "comment", "text": "@Original_LF @mimmo_rinaldi molte anzi moltissime aziende prendono dei lavori in sub anche se non hanno tutti i requisiti, soprattutto quelli della sicurezza perchè fino ad adesso ne risponde l'Azienda appaltatrice. perche secondo te è giusto?", "context": {"type": "tweet", "id": "1921944524521124090", "text": "@Original_LF @mimmo_rinaldi quello per il sub appalto delle aziende?", "author_id": "1498269845338603522", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921946382664900644", "timestamp": "2025-05-12T15:11:40+00:00", "timestamp_unix": 1747062700, "type": "comment", "text": "@Original_LF @mimmo_rinaldi non pensi sia giusto che ogni azienda si assuma le proprie responsabilità?\ne se un'azienda non ha le carte per poter fare un determinato lavoro sta a casa, o si adegua.", "context": {"type": "tweet", "id": "1921945375293767861", "text": "@Original_LF @mimmo_rinaldi molte anzi moltissime aziende prendono dei lavori in sub anche se non hanno tutti i requisiti, soprattutto quelli della sicurezza perchè fino ad adesso ne risponde l'Azienda appaltatrice. perche secondo te è giusto?", "author_id": "1498269845338603522", "author_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921946686479343927", "timestamp": "2025-05-12T15:12:52+00:00", "timestamp_unix": 1747062772, "type": "comment", "text": "@Original_LF @mimmo_rinaldi infatti in questo momento (nel tuo esempio) sono io che passo i guai non la ditta di muratori.", "context": {"type": "tweet", "id": "1921946008142909776", "text": "@Ciccioallin @mimmo_rinaldi Perché ne deve rispondere l'azienda appaltatrice.\nPerché dovrebbe risponderne chi compra i servizi?\nE' come se incriminassero te, se assumi una ditta di muratori e uno di loro casca dalla sua scala e muore.\nTu che c'entri?\nChi ne deve rispondere se non la ditta che hai assunto?", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921947077061361971", "timestamp": "2025-05-12T15:14:25+00:00", "timestamp_unix": 1747062865, "type": "comment", "text": "@Original_LF @mimmo_rinaldi si ma per una stupida regola le aziende che non superano i 16 dipendenti si rifanno sull'azienda che ha dato il lavoro in appalto.", "context": {"type": "tweet", "id": "1921946605361442832", "text": "@Ciccioallin @mimmo_rinaldi Appunto. Se invece è sul mercato, io la assumo e basta.\nSe non ha le carte, non puoi prendertela con me che l'ho assunta. Non sono io che devo controllare che LEI sia in regola.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921949273886703623", "timestamp": "2025-05-12T15:23:09+00:00", "timestamp_unix": 1747063389, "type": "comment", "text": "@Original_LF @mimmo_rinaldi Esclusione della responsabilità solidale del committente, dell'appaltatore e del subappaltatore...\nil committente è colui che ti da il lavoro in subappalto", "context": {"type": "tweet", "id": "1921947675550691547", "text": "@Ciccioallin @mimmo_rinaldi \"Il quesito chiede di eliminare la clausola che esclude la responsabilità dell'imprenditore committente se i danni sono causati da rischi specifici dell’attività dell’appaltatore o del subappaltatore.\"\n\n\"Eliminare l'esclusione del committente\"\nO capisco male io?", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921951554845770111", "timestamp": "2025-05-12T15:32:13+00:00", "timestamp_unix": 1747063933, "type": "comment", "text": "@Original_LF @mimmo_rinaldi Ok fino ad oggi se l’azienda non supera i 16 impiegati la responsabilità è del committente!", "context": {"type": "tweet", "id": "1921950676030030074", "text": "@Ciccioallin @mimmo_rinaldi Il committente è l'imprenditore che ordina il lavoro, l'appaltatore è quello che lo prende in appalto, e il subappaltatore quello che lo prende in subappalto.\nAlmeno così è come sembra  a me.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921954046769836385", "timestamp": "2025-05-12T15:42:07+00:00", "timestamp_unix": 1747064527, "type": "comment", "text": "@Original_LF @mimmo_rinaldi molte di queste aziende sono create in 10 min solo per vincere la gara e magari sono aziende di amici o parenti.", "context": {"type": "tweet", "id": "1921952392125288842", "text": "@Ciccioallin @mimmo_rinaldi Ed <PERSON> sbagliato, siamo d'accordo, ma il referendum vuole \"eliminare la clausola che ESCLUDE il committente dalle responsabilità\"\nQuindi va in senso opposto e probabilmente, dato che tale clausola già non c'è per aziende con meno di 16 impiegati, nel caso specifico non cambia nulla", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921955514461659233", "timestamp": "2025-05-12T15:47:57+00:00", "timestamp_unix": 1747064877, "type": "comment", "text": "@Original_LF @mimmo_rinaldi perdonami forse non mi sono spiegato bene,\n\nio voglio eliminare questa legge.\nper adesso la responsabilità ricade sul committente.", "context": {"type": "tweet", "id": "1921954919772225867", "text": "@Ciccioallin @mimmo_rinaldi E perché vuoi farla pagare al committente e non a loro?", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921957023236661358", "timestamp": "2025-05-12T15:53:57+00:00", "timestamp_unix": 1747065237, "type": "comment", "text": "@Original_LF @mimmo_rinaldi scusami ma non sono d'accordo.\n\nEsclusione della responsabilità... SI o NO? https://t.co/MuBAQnml0H", "context": {"type": "tweet", "id": "1921956315707875797", "text": "@Ciccioallin @mimmo_rinaldi E non è il referendum che te lo farà fare.\nIl referendum come abbiamo visto, va in senso opposto, far pagare il committente ANCHE per aziende con oltre 16 dipendenti.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921967776941437354", "timestamp": "2025-05-12T16:36:41+00:00", "timestamp_unix": 1747067801, "type": "comment", "text": "@Original_LF @mimmo_rinaldi Ho letto benissimo e so di che parlo! Ne vedo tanti amministratori di aziende in regola ma che purtroppo vengono battuti in gara da aziende farlocche! Comunque non è mio intento convincere nessuno \nGrazie per la chiacchierata", "context": {"type": "tweet", "id": "1921957675987808686", "text": "@Ciccioallin @mimmo_rinaldi ABROGAZIONE dell' ESCLUSIONE.\nLeggi bene che stanno cercando di violentarti", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921968586886619537", "timestamp": "2025-05-12T16:39:54+00:00", "timestamp_unix": 1747067994, "type": "comment", "text": "@Original_LF @mimmo_rinaldi <PERSON>za partito altrimenti non scambiavo due chiacchiere con te!", "context": {"type": "tweet", "id": "1921968312382046497", "text": "@Ciccioallin @mimmo_rinaldi Guarda ancora. C'è scritto \";Abrogazione\".\nDella norma che ESCLUDE il committente.\nFa esattamente il contrario di quello che vuoi tu.\nPoi se non mi vuoi credere per partito preso, fai pure.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921569768366072228", "tweet_id": "1921969127507259651", "timestamp": "2025-05-12T16:42:03+00:00", "timestamp_unix": 1747068123, "type": "comment", "text": "@Original_LF @mimmo_rinaldi <PERSON>, ma anche 20 min in più 👍🏻", "context": {"type": "tweet", "id": "1921968759754883320", "text": "@Ciccioallin @mimmo_rinaldi Allora spendi altri due minuti del tuo tempo, perché hai postato letteralmente tu il testo.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921569768366072228", "tweet_id": "1922238786336862389", "timestamp": "2025-05-13T10:33:34+00:00", "timestamp_unix": 1747132414, "type": "comment", "text": "@Original_LF @mimmo_rinaldi <PERSON> penso che questo chiarisce la nostra discussione di ieri. https://t.co/AAgp5JNPse", "context": {"type": "tweet", "id": "1921968759754883320", "text": "@Ciccioallin @mimmo_rinaldi Allora spendi altri due minuti del tuo tempo, perché hai postato letteralmente tu il testo.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1921569768366072228", "tweet_id": "1922262498918482382", "timestamp": "2025-05-13T12:07:48+00:00", "timestamp_unix": 1747138068, "type": "comment", "text": "@Original_LF @mimmo_rinaldi Confermo la confusione, ma la cosa che mi fa incazzare e che nessuno ne parla. Facile a dire non andate a votare, e poi?\nIo non dico di andare a votare 5 sì! Ma almeno diamo una lettura attenta ai quesiti come quello discusso insieme!", "context": {"type": "tweet", "id": "1922257976980611114", "text": "@Ciccioallin @mimmo_r<PERSON><PERSON>, per <PERSON>zza, c'è una enorme confusione con i termini, tra \"committente\" \"appaltante\" \"appaltatore\" e \"subappaltatore\"\nLa mia sensazione è che significhino quanto ti ho detto, ma sarebbe da prendersi  punto per punto gli articoli e vedere di persona.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1921569768366072228", "tweet_id": "1922263354552271047", "timestamp": "2025-05-13T12:11:12+00:00", "timestamp_unix": 1747138272, "type": "comment", "text": "@Original_LF @mimmo_rinaldi Esatto personalmente (li rileggo) ma voterò i primi 4 si! E il 5 No!", "context": {"type": "tweet", "id": "1922262839751811286", "text": "@Ciccioallin @mimmo_rinaldi Probabilmente, come pensano in molti, dei primi 4 importa zero anche ai promotori.\n\nSempre \"probabilmente\" eh, non ho la verità in tasca", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1921569768366072228", "tweet_id": "1922295347893649586", "timestamp": "2025-05-13T14:18:20+00:00", "timestamp_unix": 1747145900, "type": "comment", "text": "@Original_LF @mimmo_rinaldi si su questo hai ragione, ma si può rifiutare?", "context": {"type": "tweet", "id": "1922263660526809088", "text": "@Ciccioallin @mimmo_rinaldi <PERSON>e, se la mia interpretazione del quesito è corretta, non possono spiegartelo altrimenti propenderesti per il NO.\n\nSe la tua posizione sul 5 referendum è NO, rifiuta la scheda, non votare NO.", "author_id": "1103709792", "author_username": "Original_LF"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}]}