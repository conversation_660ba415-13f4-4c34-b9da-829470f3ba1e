{"user_id": "967737574176337920", "interactions_count": 20, "interactions": [{"conversation_id": "1909265096653652084", "tweet_id": "1909370470106808623", "timestamp": "2025-04-07T22:19:29+00:00", "timestamp_unix": 1744064369, "type": "comment", "text": "@BRICSinfo Sounds like <PERSON> had no cards in hand", "context": {"type": "tweet", "id": "1909763709464903738", "text": "🧐👀🖕\nIt’s impossible, simply impossible 😌", "author_id": "1498844762580865047", "author_username": "Pbett73Patrick"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1909450181235319202", "tweet_id": "1909664574757912734", "timestamp": "2025-04-08T17:48:09+00:00", "timestamp_unix": 1744134489, "type": "comment", "text": "@liqian_ren lol, makes so much sense.", "context": {"type": "tweet", "id": "1910411774538764384", "text": "So, it is already a solid 45% off👀", "author_id": "1234484021633179648", "author_username": "Jeytery1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 122}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910041324449141165", "timestamp": "2025-04-09T18:45:13+00:00", "timestamp_unix": 1744224313, "type": "comment", "text": "@NevondoRi @trump_repost @kanyewest Clown President", "context": {"type": "tweet", "id": "1914556900009451805", "text": "@deapthoughts @Noahpinion Weird!! He did pause here!", "author_id": "1494812868948152321", "author_username": "MetacriticCap"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 105}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910041820136137110", "timestamp": "2025-04-09T18:47:11+00:00", "timestamp_unix": 1744224431, "type": "comment", "text": "Whatever China wins or not, China had the respect from the world, Humble, consistent and respect to other countries. While America is exhausting its allies resource and squeeze every penny from their allies. MAGA thinkings the rest of the world stolen their jobs or money, but to be honest American products just pure shits. American cars is not even the best selling car domestically", "context": {"type": "tweet", "id": "1914556900009451805", "text": "@deapthoughts @Noahpinion Weird!! He did pause here!", "author_id": "1494812868948152321", "author_username": "MetacriticCap"}, "metrics": {"retweet_count": 10, "reply_count": 20, "like_count": 213, "quote_count": 1, "view_count": 9330}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910045392861123016", "timestamp": "2025-04-09T19:01:23+00:00", "timestamp_unix": 1744225283, "type": "comment", "text": "@Gee323781865105 @trump_repost 哈哈 继续舔你的白人鸡吧", "context": {"type": "tweet", "id": "1910045206948483573", "text": "@alikeWong @trump_repost 赢你吗的尊重 你知道现在全世界都在针对中国吗 因为你抢走了他们的饭碗转了他们的钱", "author_id": "1809660418614603776", "author_username": "Gee323781865105"}, "metrics": {"retweet_count": 1, "reply_count": 2, "like_count": 19, "quote_count": 0, "view_count": 921}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910045628715188578", "timestamp": "2025-04-09T19:02:19+00:00", "timestamp_unix": 1744225339, "type": "comment", "text": "@w_s_gosset @trump_repost Lets see, now EU just joined China for retaliation. And in the future, you think other countries are willing to trade with China or a psycho USA?", "context": {"type": "tweet", "id": "1914556900009451805", "text": "@deapthoughts @Noahpinion Weird!! He did pause here!", "author_id": "1494812868948152321", "author_username": "MetacriticCap"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 64}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910058455244955904", "timestamp": "2025-04-09T19:53:17+00:00", "timestamp_unix": 1744228397, "type": "comment", "text": "@Pankekke<PERSON> @trump_repost Why dont you ask <PERSON> to stop being pshyco. Now he postponed other tarrifs for 90 days. You think how much the other countries can endure this type of shits", "context": {"type": "tweet", "id": "1910033417154031667", "text": "@trump_repost China just say please\n\nits not that hard", "author_id": "832218563964444672", "author_username": "Pankekkeku"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 6, "quote_count": 0, "view_count": 1652}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910058524224405757", "timestamp": "2025-04-09T19:53:34+00:00", "timestamp_unix": 1744228414, "type": "comment", "text": "@theverycob @trump_repost 4000% make it", "context": {"type": "tweet", "id": "1914556900009451805", "text": "@deapthoughts @Noahpinion Weird!! He did pause here!", "author_id": "1494812868948152321", "author_username": "MetacriticCap"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 190}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910058573113205159", "timestamp": "2025-04-09T19:53:45+00:00", "timestamp_unix": 1744228425, "type": "comment", "text": "@M1sense @theverycob @trump_repost Boom boom pow lol", "context": {"type": "tweet", "id": "1910048128126050746", "text": "@theverycob @trump_repost do  911%", "author_id": "1585667693234991104", "author_username": "M1sense"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 100}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910078667436544057", "timestamp": "2025-04-09T21:13:36+00:00", "timestamp_unix": 1744233216, "type": "comment", "text": "@Willworld1930 @trump_repost Yeah, for drug dealers", "context": {"type": "tweet", "id": "1914556900009451805", "text": "@deapthoughts @Noahpinion Weird!! He did pause here!", "author_id": "1494812868948152321", "author_username": "MetacriticCap"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 40}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910161301659169225", "timestamp": "2025-04-10T02:41:58+00:00", "timestamp_unix": 1744252918, "type": "comment", "text": "@wangqin83644743 @Gee323781865105 @trump_repost 这些无所谓，只是这一次真是涨见识了， MAGA+大殖子们的智商果然不在线， 挺好的， 我们这一代人这辈子有机会见到东大一统世界取代米国", "context": {"type": "tweet", "id": "1910160054160072961", "text": "@Gee323781865105 @alikeWong @trump_repost 打了很多字，又删除了，对你这种逗比，只能送你一个词“傻逼”，多了你也理解不了", "author_id": "1639949812744474628", "author_username": "wangqin83644743"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 29}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910172723135107309", "timestamp": "2025-04-10T03:27:21+00:00", "timestamp_unix": 1744255641, "type": "comment", "text": "@Big1616Big @trump_repost it is true, it just feel like mocking other world leaders. real american people and chinese people are always friends and allied since WWII", "context": {"type": "tweet", "id": "1910169488647151643", "text": "@alikeWong @trump_repost We seem to have won, but we have lost the whole world. Most countries dare not speak out against us.", "author_id": "1497743280359964676", "author_username": "Big1616Big"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910322125178917155", "timestamp": "2025-04-10T13:21:01+00:00", "timestamp_unix": 1744291261, "type": "comment", "text": "@posley93264 @t_bomberdill @trump_repost <PERSON><PERSON> and greedy Americans wants to squeeze every penny from his allies. And blaming all this to hard working Chinese and claiming they steal the jobs and money.", "context": {"type": "tweet", "id": "1910283573401460842", "text": "@t_bomberdill @alikeWong @trump_repost Bro, according to your logic, the whole world owes money to Americans? I seriously doubt whether you even went to high school...", "author_id": "1795026862328213504", "author_username": "posley93264"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1910019919628120289", "tweet_id": "1910355735323775453", "timestamp": "2025-04-10T15:34:34+00:00", "timestamp_unix": 1744299274, "type": "comment", "text": "@t_bomberdill @posley93264 @trump_repost Dude, all your words makes no statement but purely hate and jealousy. Is this what MAGAs all about? Im glad to see this happening in America. Its gonna collapse faster", "context": {"type": "tweet", "id": "1914556900009451805", "text": "@deapthoughts @Noahpinion Weird!! He did pause here!", "author_id": "1494812868948152321", "author_username": "MetacriticCap"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1912152941814759710", "tweet_id": "1912158853925384542", "timestamp": "2025-04-15T14:59:31+00:00", "timestamp_unix": 1744729171, "type": "comment", "text": "@Christos_Vet @BRICSinfo arrogant americans", "context": {"type": "tweet", "id": "1912153680293265742", "text": "@BRICSinfo Is that why 130 countries are in contact with Trump?\n\nChina will soon negotiate", "author_id": "2800479744", "author_username": "Christos_Vet"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 12, "quote_count": 0, "view_count": 408}}, {"conversation_id": "1912263329885585602", "tweet_id": "1912345675238621379", "timestamp": "2025-04-16T03:21:53+00:00", "timestamp_unix": 1744773713, "type": "comment", "text": "@ABCChinese 这女的有点新型战狼外交官的感觉了", "context": {"type": "tweet", "id": "1912895965708918863", "text": "你们美国都那么众星捧月了，干嘛操心中国的事。你美国自己玩就好了", "author_id": "1354595043936354305", "author_username": "tianjie86092931"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 1078}}, {"conversation_id": "1912368318432075915", "tweet_id": "1912503437683499306", "timestamp": "2025-04-16T13:48:46+00:00", "timestamp_unix": 1744811326, "type": "comment", "text": "@thecyrusjanssen so far china gained more trading partners, exchanged EU market with ev cars and replacing boeing to Airbus. the world works just fine without USA", "context": {"type": "tweet", "id": "1912934448481575284", "text": "川普无论怎么做，都会让中国再次伟大。", "author_id": "886808003579568128", "author_username": "hsd8899"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 31, "quote_count": 0, "view_count": 1526}}, {"conversation_id": "1912368318432075915", "tweet_id": "1912503662901096674", "timestamp": "2025-04-16T13:49:40+00:00", "timestamp_unix": 1744811380, "type": "comment", "text": "@BachmannHelle @cnewton_ky @thecyrusjanssen lol exactly, Korea peninsula problem solved when China lead, same as Ukraine and Russia. China provides a better offer than Trump to Ukraine.", "context": {"type": "tweet", "id": "1912462691492331541", "text": "@cnewton_ky @thecyrusjanssen If all countries stoped following American policies and agendas, there wouldn't be many enemies to be defended from.", "author_id": "1282241857175552000", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 98}}, {"conversation_id": "1914350606153634238", "tweet_id": "1914357225864478956", "timestamp": "2025-04-21T16:35:04+00:00", "timestamp_unix": 1745253304, "type": "comment", "text": "@BRICSinfo I believe it bypassed USD as well.", "context": {"type": "tweet", "id": "1917024058027499556", "text": "既にUAE政府企業は数兆ドルを米国で運用しており更に10年間1.4兆ドル(約208兆円)の投資を米国と約束している。\n\n中国は5年間の天然ガスの契約を敢えてUAEを選んだのだろう。\n\nUAEはガスも売れて米国株や企業も安価で購入できて米国は文句を言えない。\n\n米国潰しの絵を描くネズミが内部に居そうだな〜", "author_id": "103411189", "author_username": "Satoshi512JAPAN"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 8, "quote_count": 0, "view_count": 5157}}, {"conversation_id": "1914627741611385265", "tweet_id": "1914816040011489795", "timestamp": "2025-04-22T22:58:14+00:00", "timestamp_unix": 1745362694, "type": "comment", "text": "@LegitTargets i beilieve it alsy bypassed US swift system.", "context": {"type": "tweet", "id": "1914907556968808589", "text": "Communist China getting its LNG from United Arab Emirates.", "author_id": "1787643216969289729", "author_username": "JungleBall_2"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 150}}]}