{"user_id": "1710847140233129984", "interactions_count": 17, "interactions": [{"conversation_id": "1917222450338623513", "tweet_id": "1917410446887326031", "timestamp": "2025-04-30T02:47:28+00:00", "timestamp_unix": 1745981248, "type": "comment", "text": "@DailyNoahNews @WatcherGuru Lol, and vice versa", "context": {"type": "tweet", "id": "1917234778396561837", "text": "@WatcherGuru That’s the <PERSON> effect in motion. China doesn’t just roll back a 125% tariff out of kindness, they’re reacting to pressure. This is why tariffs work when you’ve got a president who actually negotiates from strength. American energy wins, and China blinks.", "author_id": "1537219381649977344", "author_username": "DailyNoahNews"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917410600751362334", "timestamp": "2025-04-30T02:48:05+00:00", "timestamp_unix": 1745981285, "type": "comment", "text": "@VasBroughtToX @WatcherGuru But unfortunately, not for us Americans", "context": {"type": "tweet", "id": "1917222812479291417", "text": "@WatcherGuru The art of the deal is working for <PERSON>", "author_id": "1809667956957458432", "author_username": "VasBroughtToX"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 49}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917410658146021450", "timestamp": "2025-04-30T02:48:19+00:00", "timestamp_unix": 1745981299, "type": "comment", "text": "@ZanRogan @WatcherGuru Not even hardly", "context": {"type": "tweet", "id": "1917230060559618430", "text": "@WatcherGuru This is what winning looks like. Good job President <PERSON>, thank you for making America great again", "author_id": "1395749672623427588", "author_username": "ZanRogan"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917410958269427805", "timestamp": "2025-04-30T02:49:30+00:00", "timestamp_unix": 1745981370, "type": "comment", "text": "@TheHoopFather @WatcherGuru Doubt it", "context": {"type": "tweet", "id": "1917237097578303825", "text": "@WatcherGuru I guess China and Trump are talking like he said…", "author_id": "1530212717692235777", "author_username": "TheHoopFather"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 64}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917411388391096499", "timestamp": "2025-04-30T02:51:13+00:00", "timestamp_unix": 1745981473, "type": "comment", "text": "@alicecyberspace @WatcherGuru Oh while, he is golfing and yelling on X all night…\nNot hardly", "context": {"type": "tweet", "id": "1917222865981653337", "text": "@WatcherGuru Trump playing 3D chess with China while Amazon tries to start drama - classic corporate cope when the market shifts", "author_id": "1861073969475895296", "author_username": "alicecyberspace"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917412382512493005", "timestamp": "2025-04-30T02:55:10+00:00", "timestamp_unix": 1745981710, "type": "comment", "text": "@HorsePilled_PFL @WatcherGuru 🤪", "context": {"type": "tweet", "id": "1917378011655004568", "text": "@WatcherGuru And so it begins \n\n<PERSON> was always right", "author_id": "1438561888330190849", "author_username": "HorsePilled_PFL"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917412453027062184", "timestamp": "2025-04-30T02:55:27+00:00", "timestamp_unix": 1745981727, "type": "comment", "text": "@alifarhat79 @WatcherGuru Not even hardly", "context": {"type": "tweet", "id": "1917223264381776325", "text": "@WatcherGuru Xi caved. <PERSON> won. Next", "author_id": "105852422", "author_username": "alifarhat79"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 105}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917412780543730172", "timestamp": "2025-04-30T02:56:45+00:00", "timestamp_unix": 1745981805, "type": "comment", "text": "@testtheorybtc @WatcherGuru Doubt it, keep watching", "context": {"type": "tweet", "id": "1917227063171334178", "text": "@WatcherGuru China is folding", "author_id": "932775154630963200", "author_username": "testtheorybtc"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917413057753461199", "timestamp": "2025-04-30T02:57:51+00:00", "timestamp_unix": 1745981871, "type": "comment", "text": "@FroggerXBT @WatcherGuru No, but the fact that he’s destroyed all of our relationships around the world is hardly a good move", "context": {"type": "tweet", "id": "1917222786235339072", "text": "@WatcherGuru They just realized they weren't going to beat <PERSON> at 10D chess", "author_id": "2299498338", "author_username": "FroggerXBT"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917413205879542069", "timestamp": "2025-04-30T02:58:26+00:00", "timestamp_unix": 1745981906, "type": "comment", "text": "@BanzaiBodhi @WatcherGuru Nope", "context": {"type": "tweet", "id": "1917223714489315517", "text": "@WatcherGuru Wow, is China caving?", "author_id": "1823090934634139648", "author_username": "BanzaiBodhi"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917413272887665026", "timestamp": "2025-04-30T02:58:42+00:00", "timestamp_unix": 1745981922, "type": "comment", "text": "@Benzinga @WatcherGuru Small is right", "context": {"type": "tweet", "id": "1917223173289894222", "text": "@WatcherGuru Small win for US energy exporters.", "author_id": "44060322", "author_username": "Benzinga"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917413375950348590", "timestamp": "2025-04-30T02:59:07+00:00", "timestamp_unix": 1745981947, "type": "comment", "text": "@BryptoJoe @WatcherGuru Both will bend", "context": {"type": "tweet", "id": "1917222669365174415", "text": "@WatcherGuru Bend the knee China", "author_id": "1430346057230204933", "author_username": "BryptoJoe"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917413568057663683", "timestamp": "2025-04-30T02:59:53+00:00", "timestamp_unix": 1745981993, "type": "comment", "text": "@BryptoJoe @WatcherGuru Do you know how much of everything we have covers from China look at all your dishes and statues and clothes and electronics etc. etc.", "context": {"type": "tweet", "id": "1917222669365174415", "text": "@WatcherGuru Bend the knee China", "author_id": "1430346057230204933", "author_username": "BryptoJoe"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917413816494653681", "timestamp": "2025-04-30T03:00:52+00:00", "timestamp_unix": 1745982052, "type": "comment", "text": "@alicecyberspace @WatcherGuru An automated reply wow, can’t you all do a little better?", "context": {"type": "tweet", "id": "1917412018447081726", "text": "@PatFernand80663 @WatcherGuru Sounds like someone's salty about those tariff reductions making their shorts go underwater", "author_id": "1861073969475895296", "author_username": "alicecyberspace"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917415850954330249", "timestamp": "2025-04-30T03:08:57+00:00", "timestamp_unix": 1745982537, "type": "comment", "text": "@alicecyberspace @WatcherGuru Well, automated reply, I hardly fit into the description of someone sitting in their mother‘s basement, try again try criticizing me for being an educated senior citizen who worked in the federal courts and I probably won’t respond why? ZZZ", "context": {"type": "tweet", "id": "1917414349435556332", "text": "@PatFernand80663 @WatcherGuru Nah sweetie, I'm just naturally this witty - unlike those NPCs still coping about tariffs in their mom's basement", "author_id": "1861073969475895296", "author_username": "alicecyberspace"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1917222450338623513", "tweet_id": "1917565241904238651", "timestamp": "2025-04-30T13:02:34+00:00", "timestamp_unix": 1746018154, "type": "comment", "text": "@alicecyberspace @WatcherGuru ? Sure", "context": {"type": "tweet", "id": "1917412018447081726", "text": "@PatFernand80663 @WatcherGuru Sounds like someone's salty about those tariff reductions making their shorts go underwater", "author_id": "1861073969475895296", "author_username": "alicecyberspace"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}, {"conversation_id": "1917222450338623513", "tweet_id": "1921176267522494941", "timestamp": "2025-05-10T12:11:30+00:00", "timestamp_unix": 1746879090, "type": "comment", "text": "@alicecyberspace @WatcherGuru Well sweetie pie I don’t think witty is quite the way to describe you", "context": {"type": "tweet", "id": "1917414349435556332", "text": "@PatFernand80663 @WatcherGuru Nah sweetie, I'm just naturally this witty - unlike those NPCs still coping about tariffs in their mom's basement", "author_id": "1861073969475895296", "author_username": "alicecyberspace"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 1}}]}