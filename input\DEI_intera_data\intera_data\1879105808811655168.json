{"user_id": "1879105808811655168", "interactions_count": 30, "interactions": [{"conversation_id": "1920002406600855825", "tweet_id": "1920207283629207841", "timestamp": "2025-05-07T20:01:06+00:00", "timestamp_unix": 1746648066, "type": "comment", "text": "@stebaraz Glielo avranno suggerito i suoi amichetti fanatici fascisti ammazza-bambini? https://t.co/F9pkHGkpDF", "context": {"type": "tweet", "id": "1920046885957419132", "text": "Un vero pacifista, non c'è che dire\n\nhttps://t.co/fOMWTHQU81", "author_id": "47373547", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1920393359807357348", "tweet_id": "1920402344568627206", "timestamp": "2025-05-08T08:56:12+00:00", "timestamp_unix": 1746694572, "type": "comment", "text": "@fgavazzoni E questo cosa ha a che fare con l'usare le facce degli ostaggi per giustificare la pulizia etnica dei palestinesi?\n\nCredi ancora che Adolf Netanyahu stia radendo al suolo Gaza per \"trovare\" gli ostaggi? https://t.co/4i1FpKHOr5", "context": {"type": "tweet", "id": "1920777847943090677", "text": "Io ho visitato Budapest e la sua splendida sinagoga, la più grande d’Europa, e le guide ci hanno spiegato bene cosa è successo agli ebrei locali in quei terribili anni. Ma questi sono tutti i dettagli insignificanti agli occhi della somma giornalista. Lo schifo", "author_id": "2429648445", "author_username": "marcoorioles"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 44}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920731454453039395", "timestamp": "2025-05-09T06:43:58+00:00", "timestamp_unix": 1746773038, "type": "comment", "text": "@mrctrdsh Oggi è la festa della Vittoria dell'URSS sul Terzo Reich, anche se a qualcuno ancora rode... https://t.co/lbnSuBJFud", "context": {"type": "tweet", "id": "1921114314481484056", "text": "E che c'entra la nazicrimimale Ucraina con l'Europa??? VOI VOLTE LA GUERRA ATOMICA, MASSA DI GUERRAFONDAI NAZISTOODI!", "author_id": "1779862862359359488", "author_username": "Loveman031"}, "metrics": {"retweet_count": 0, "reply_count": 4, "like_count": 14, "quote_count": 0, "view_count": 176}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920777761632731529", "timestamp": "2025-05-09T09:47:59+00:00", "timestamp_unix": 1746784079, "type": "comment", "text": "@Gliamicidisemp1 @mrctrdsh Quando Francia e UK si sono rifiutati di allearsi con la Russia per sconfiggere la Germania.\nA quel punto la Russia, temendo la disastrosa invasione tedesca (avvenuta comunque qualche anno dopo), ha ottenuto un patto di non aggressione. https://t.co/VuLPHfD0dl", "context": {"type": "tweet", "id": "1920743873237615065", "text": "@MrJFCGauss80 @mrctrdsh ..mi ricorda quale è invece l'anniversario del patto hitler-stalin che ha dato il via alla wwii?\n\n..e l'anniversario della crollo della cortina di ferro con cui l'URSS ha oppresso mezza europa \"liberata dal nazismo\"?\n\n..e perchè ogni anno dalla piazza rossa si minaccia il mondo?", "author_id": "1416392619794571265", "author_username": "Gliamicidisemp1"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920780497224859693", "timestamp": "2025-05-09T09:58:51+00:00", "timestamp_unix": 1746784731, "type": "comment", "text": "@mrctrdsh Le solite puttanate della propaganda neonazista che vuole far passare il patto di non aggressione Molotov-Ribbentrop (come quello firmato da UK e Germania nel 1938) con un'alleanza (come quella tra Italia e Germania del 1939). https://t.co/ltCxqDp7fD", "context": {"type": "tweet", "id": "1920757315453534553", "text": "@MrJFCGauss80 Voi putinisti rimpiangete ancora la prima fase della guerra, quando <PERSON> e <PERSON> erano alleati.", "author_id": "50355444", "author_username": "mrctrdsh"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920812512305426590", "timestamp": "2025-05-09T12:06:04+00:00", "timestamp_unix": 1746792364, "type": "comment", "text": "@Gliamicidisemp1 @mrctrdsh Perchè sono sempre le solite puttanate dei neonazisti che non sanno cosa cazzo è un patto di non aggressione https://t.co/MhGnR7ym1U", "context": {"type": "tweet", "id": "1920796285293965761", "text": "@MrJFCGauss80 @mrctrdsh ..perchè omettere sempre che il \"patto di non aggressione\" è\nil patto con cui stalin diede il via libera ad hitler per la spartizione congiunta della Polonia con l'URSS e quindi il via alla seconda guerra mondiale?\n\n..basta conoscere un minimo (minimo) di storia", "author_id": "1416392619794571265", "author_username": "Gliamicidisemp1"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920820761876402620", "timestamp": "2025-05-09T12:38:51+00:00", "timestamp_unix": 1746794331, "type": "comment", "text": "@Giovann70297287 @mrctrdsh Non è che se ripeti sempre la stessa puttanata allora la gente ti deve dar ragione... https://t.co/aHKCYYu1BX", "context": {"type": "tweet", "id": "1920815209557434737", "text": "@MrJFCGauss80 @mrctrdsh Nessuna puttanata. Con il il patto di Monaco del 1938 inglesi e francesi cercarono di evitare la guerra. Il patto fra Stalin e Hitler del 1939 invece servi' ad Hitler per dare inizio alla guerra. Quindi tutto l'opposto. <PERSON> ed <PERSON> inoltre decisero di spartirsi la Polonia.", "author_id": "1341420668131438593", "author_username": "Giovann70297287"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920826667058008312", "timestamp": "2025-05-09T13:02:19+00:00", "timestamp_unix": 1746795739, "type": "comment", "text": "@Giovann70297287 @Gliamicidisemp1 @mrctrdsh Le solite puttanate della propaganda neonazista.\n\nGratta gratta, sotto ogni liberista si nasconde sempre un nazifascista represso.\nSempre!", "context": {"type": "tweet", "id": "1920825490950021233", "text": "@MrJFCGauss80 @Gliamicidisemp1 @mrctrdsh La grande differenza, che voi comunisti adoratori di quell'assassino di <PERSON>, non volete capire, sta nel fatto che quel patto servi per permettere alla Germania di dare il via alla guerra. Come avvenne solo qualche giorno dopo. E con vantaggi territoriali per l'assassino Stalin", "author_id": "1341420668131438593", "author_username": "Giovann70297287"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920826921786462571", "timestamp": "2025-05-09T13:03:20+00:00", "timestamp_unix": 1746795800, "type": "comment", "text": "@Giovann70297287 @Gliamicidisemp1 @mrctrdsh Chiudi twitter e apri un libro di storia, naziliberista analfabeta!", "context": {"type": "tweet", "id": "1920826068258185709", "text": "@MrJFCGauss80 @Gliamicidisemp1 @mrctrdsh Non sono puttanate. Siete voi comunisti che non volete capire che quel vergognoso patto servi a Hitler per dare inizio alla guerra.", "author_id": "1341420668131438593", "author_username": "Giovann70297287"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920828587285832098", "timestamp": "2025-05-09T13:09:57+00:00", "timestamp_unix": 1746796197, "type": "comment", "text": "@Giovann70297287 @Gliamicidisemp1 @mrctrdsh 😂😂😂 https://t.co/rxjpCTB0LB", "context": {"type": "tweet", "id": "1920828213669867889", "text": "@MrJFCGauss80 @Gliamicidisemp1 @mrctrdsh Gratta gratta alla fine vi dimostrate i comunisti di sempre. Adoratori dei maggiori delinquenti e assassini che la storia abbia mai conosciuto. <PERSON>, <PERSON> e <PERSON>. Messi tutti assieme hanno causato quasi 100 milioni di morti. Tragedia immensa. Ma voi zitti. Vergogna", "author_id": "1341420668131438593", "author_username": "Giovann70297287"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920828977200963654", "timestamp": "2025-05-09T13:11:30+00:00", "timestamp_unix": 1746796290, "type": "comment", "text": "@Giovann70297287 @Gliamicidisemp1 @mrctrdsh Hai rotto i coglioni a ripetermi sempre la stessa cazzata come un pappagallo con l'Alzheimer", "context": {"type": "tweet", "id": "1920823800679071789", "text": "@MrJFCGauss80 @Gliamicidisemp1 @mrctrdsh Diciamo che il patto fra comunisti e nazisti è  stato vergognoso. Permise a Hitler di dare inizio alla guerra e servi per spartire la Polonia fra tedeschi e russi. Mai avrebbero combattuto <PERSON> se quel pazzo  non avesse invaso la Russia. E i capi comunisti tutti con Stalin", "author_id": "1341420668131438593", "author_username": "Giovann70297287"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920829359591428362", "timestamp": "2025-05-09T13:13:01+00:00", "timestamp_unix": 1746796381, "type": "comment", "text": "@Gliamicidisemp1 @mrctrdsh Succede quando sei un analfabeta naziliberista che ripete a pappagallo le puttanate che legge su twitter e che non sa cosa sia un patto di non aggressione...", "context": {"type": "tweet", "id": "1920828990916346116", "text": "@MrJFCGauss80 @mrctrdsh Guarda il caso,\nla coincidenza sbalorditiva\nche hitler e stalin sentirono l'esigenza improvvisa di invadere la Polonia a 16 giorni di distanza l'uno dall'altro..\n(indipendentemente,\nsia chiaro dato che il patto era di \"non aggressione\"\nnon di spartizione della polonia)\n\nBuffonate", "author_id": "1416392619794571265", "author_username": "Gliamicidisemp1"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920932864218083582", "timestamp": "2025-05-09T20:04:18+00:00", "timestamp_unix": 1746821058, "type": "comment", "text": "@ipdsipf @CSardesco @mrctrdsh Solo i naziliberisti equiparano quelli che hanno cost<PERSON><PERSON> a quelli che l'hanno liberata...", "context": {"type": "tweet", "id": "1920916811450659318", "text": "@CSardesco @mrctrdsh @MrJFCGauss80 Non sono i colori di rappresentanza a fare la differenza: grave nel 2025 non aver raggiunto ancora la giusta distanza per capire che quel che chiami nazistimo e quel che chiami comunismo, non sono due opposti, ma solo due nomi diversi dati allo stesso puzzo.", "author_id": "2948915277", "author_username": "ipdsipf"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920935589848756718", "timestamp": "2025-05-09T20:15:08+00:00", "timestamp_unix": 1746821708, "type": "comment", "text": "@ipdsipf @CSardesco @mrctrdsh https://t.co/rGR6PnOYKP", "context": {"type": "tweet", "id": "1920933618257362963", "text": "@MrJFCGauss80 @CSardesco @mrctrdsh No, i nazistelli si professano argine contro il nazismo con metodi nazisti. Chi non è nazista non fa il nazista.", "author_id": "2948915277", "author_username": "ipdsipf"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920938760700588537", "timestamp": "2025-05-09T20:27:44+00:00", "timestamp_unix": 1746822464, "type": "comment", "text": "@CSardesco @ipdsipf @mrctrdsh E' per il fascioliberista che si racconta puttanate per autoconvincersi di non essere un fascista represso...", "context": {"type": "tweet", "id": "1920937268610564272", "text": "@MrJFCGauss80 @ipdsipf @mrctrdsh Non ho capito", "author_id": "940949850547458048", "author_username": "CSardesco"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920943917433839782", "timestamp": "2025-05-09T20:48:13+00:00", "timestamp_unix": 1746823693, "type": "comment", "text": "@ipdsipf @CSardesco @mrctrdsh Pa<PERSON><PERSON> di <PERSON> e <PERSON> quando fecero bombardare ILLEGALMENTE la Serbia dalla Nato, giusto?", "context": {"type": "tweet", "id": "1920942048942641570", "text": "@MrJFCGauss80 @CSardesco @mrctrdsh Non ho bisogno di convincermi di niente, non sono io a tifare per il criminale che ha riportato la guerra in Europa", "author_id": "2948915277", "author_username": "ipdsipf"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920949601562997119", "timestamp": "2025-05-09T21:10:49+00:00", "timestamp_unix": 1746825049, "type": "comment", "text": "@ipdsipf @CSardesco @mrctrdsh Le stesse merdate che racconta la Meloni... e dice pure di non essere un fascista represso!", "context": {"type": "tweet", "id": "1920945655322681763", "text": "@MrJFCGauss80 @CSardesco @mrctrdsh Il grande classico della colpevolizzazione dell'occidente. La democrazia vi fa schifo ma credete di non essere fascisti", "author_id": "2948915277", "author_username": "ipdsipf"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1920722694347141629", "tweet_id": "1920954327583064450", "timestamp": "2025-05-09T21:29:35+00:00", "timestamp_unix": 1746826175, "type": "comment", "text": "@ipdsipf @CSardesco @mrctrdsh Eja eja alalà...\nhttps://t.co/E9z8MkkwXN", "context": {"type": "tweet", "id": "1920951684341088409", "text": "@MrJFCGauss80 @CSardesco @mrctrdsh È un fenomeno osservabile, a parole puoi convincerti di quello che ti pare, ma appena sopra sei inciampato nel solito cliché antioccidentale.", "author_id": "2948915277", "author_username": "ipdsipf"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921104857181614303", "tweet_id": "1921105365258637585", "timestamp": "2025-05-10T07:29:46+00:00", "timestamp_unix": 1746862186, "type": "comment", "text": "@ilaborletti Queste sono le cazzate di Adolf Netanyahu e della sua banda di porci nazisti per giustificare #GazaGenocide", "context": {"type": "tweet", "id": "1921121415253655778", "text": "Ma... https://t.co/60KV1hCflv", "author_id": "990487487125520385", "author_username": "CinziaSally"}, "metrics": {"retweet_count": 1, "reply_count": 1, "like_count": 9, "quote_count": 0, "view_count": 131}}, {"conversation_id": "1921104857181614303", "tweet_id": "1921107275156013290", "timestamp": "2025-05-10T07:37:21+00:00", "timestamp_unix": 1746862641, "type": "comment", "text": "@EdoCira @ilaborletti Torna a farti le pippe davanti ai poster in cameretta di Adolf Netanyahu e del porco fascista Ben Gvir, scarafaggio nazisionista.", "context": {"type": "tweet", "id": "1921121415253655778", "text": "Ma... https://t.co/60KV1hCflv", "author_id": "990487487125520385", "author_username": "CinziaSally"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1921104857181614303", "tweet_id": "1921114976183296215", "timestamp": "2025-05-10T08:07:57+00:00", "timestamp_unix": 1746864477, "type": "comment", "text": "@EdoCira @ilaborletti Ma levati dai coglioni, scarafaggio nazisionista. https://t.co/WDUdSVRmC9", "context": {"type": "tweet", "id": "1921121415253655778", "text": "Ma... https://t.co/60KV1hCflv", "author_id": "990487487125520385", "author_username": "CinziaSally"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1920722694347141629", "tweet_id": "1921178118640779460", "timestamp": "2025-05-10T12:18:51+00:00", "timestamp_unix": 1746879531, "type": "comment", "text": "@Gio_Mex @mrctrdsh Le spese belliche sostenute dall'URSS per la WWII sono stimate attorno ai 192 Mld di dollari.\nIl Lend Lease per la Russia valeva 11 Mld, spalmati su 4 anni e la maggior parte arrivati verso la fine della guerra.\nPoco più del 5%: utili ma non indispensabili.", "context": {"type": "tweet", "id": "1921174977937568107", "text": "@MrJFCGauss80 @mrctrdsh <PERSON>, come tutti gli altri giorni dell'anno, è la festa della vittoria della potenza degli USA che ha permesso a quelli che avevano perduto tutta l'Europa, dai confini con la Spagna a pochi chilometri di distanza da <PERSON>sca, di poter ribaltare la situazione grazie al loro aiuto.", "author_id": "52071674", "author_username": "Gio_Mex"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921174320920842634", "tweet_id": "1921180918284173464", "timestamp": "2025-05-10T12:29:59+00:00", "timestamp_unix": 1746880199, "type": "comment", "text": "@giorgio_gori Vi siete svegliati solo dopo lo sterminio di 20.000 bambini?\nFino a ieri tutti in gara per farsi foto coi fanatici genocidi e oggi vi ricordate di #GazaGenocide?\nhttps://t.co/vqowlWX6q4", "context": {"type": "tweet", "id": "1921820543654801767", "text": "Vergognate<PERSON> di parlare solo ora...dove eravate...siete tutti complici...\nL'Europa per prima.\n#Europa", "author_id": "1505616048787279875", "author_username": "luna40044688"}, "metrics": {"retweet_count": 2, "reply_count": 1, "like_count": 8, "quote_count": 0, "view_count": 120}}, {"conversation_id": "1921207997902033168", "tweet_id": "1921208903645147510", "timestamp": "2025-05-10T14:21:11+00:00", "timestamp_unix": 1746886871, "type": "comment", "text": "@MT_<PERSON>i_ Questo sarà il 6° o 7° inutile pellegrinaggio a Kiev del Napoleone della soia...", "context": {"type": "tweet", "id": "1921529063044206827", "text": "Si crede un Novello #Napoleone #Macron \n\nhttps://t.co/VzWr4TJymI \n\nhttps://t.co/LFNwl2Na9P", "author_id": "849376267090763776", "author_username": "<PERSON>_<PERSON>_DDP"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 45}}, {"conversation_id": "1921208303759020476", "tweet_id": "1921216636435968388", "timestamp": "2025-05-10T14:51:55+00:00", "timestamp_unix": 1746888715, "type": "comment", "text": "@gpazzaglia71 Che c'è da commentare?\nhttps://t.co/ndgkOQrwmj", "context": {"type": "tweet", "id": "1921208303759020476", "text": "原始推文内容不可用", "author_id": "624096392", "author_username": "gpazzaglia71"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 172}}, {"conversation_id": "1921208303759020476", "tweet_id": "1921221385587695623", "timestamp": "2025-05-10T15:10:47+00:00", "timestamp_unix": 1746889847, "type": "comment", "text": "@aa4323801448919 @gpazzaglia71 Sai leggere l'inglese, analfabeta? https://t.co/VcnRp9NSVQ", "context": {"type": "tweet", "id": "1921220704399163601", "text": "@MrJFCGauss80 @gpazzaglia71 Certo certo due colpi di fucile da guerra in testa e vieni ricoverato in ospedale...\nNon capisco dove finisca le malafede dei ProPal e inizi l'ignoranza più crassa...", "author_id": "1713983129810083840", "author_username": "aa4323801448919"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1921208303759020476", "tweet_id": "1921224056101036509", "timestamp": "2025-05-10T15:21:24+00:00", "timestamp_unix": 1746890484, "type": "comment", "text": "@aa4323801448919 @gpazzaglia71 Devo dar retta al The Guardian o al primo scarafaggio nazisionista di merda che fa l'esperto del cazzo su twitter?\nChe dilemma\n🤔🤔🤔", "context": {"type": "tweet", "id": "1921223388275581133", "text": "@MrJFCGauss80 @gpazzaglia71 Tu sei un povero coglione che crede che questa sia la radiografia di un bambino con un proiettile in testa...\nCome se i proettili rimanessero integri...\nSei troppo coglione... https://t.co/E2VNjj1z8h", "author_id": "1713983129810083840", "author_username": "aa4323801448919"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1921208303759020476", "tweet_id": "1921224608767774750", "timestamp": "2025-05-10T15:23:36+00:00", "timestamp_unix": 1746890616, "type": "comment", "text": "@aa4323801448919 @gpazzaglia71 \"The Guardian is Khamas\" https://t.co/mgsA4h18JV", "context": {"type": "tweet", "id": "1921224453939253657", "text": "@MrJFCGauss80 @gpazzaglia71 Il Guardian nota fonte affidabilissima LOL", "author_id": "1713983129810083840", "author_username": "aa4323801448919"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1921174320920842634", "tweet_id": "1921229577042473356", "timestamp": "2025-05-10T15:43:20+00:00", "timestamp_unix": 1746891800, "type": "comment", "text": "@giorgio_gori https://t.co/FXDrKiy9JP", "context": {"type": "tweet", "id": "1921180918284173464", "text": "@giorgio_gori Vi siete svegliati solo dopo lo sterminio di 20.000 bambini?\nFino a ieri tutti in gara per farsi foto coi fanatici genocidi e oggi vi ricordate di #GazaGenocide?\nhttps://t.co/vqowlWX6q4", "author_id": "1879105808811655168", "author_username": "MrJFCGauss80"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 3}}, {"conversation_id": "1921641561403580806", "tweet_id": "1921642241786777931", "timestamp": "2025-05-11T19:03:07+00:00", "timestamp_unix": 1746990187, "type": "comment", "text": "@MT_Meli_ I famigerati superiori valori morali europei...", "context": {"type": "tweet", "id": "1922969953411408036", "text": "Niente da dire qua? 🤡 https://t.co/ftqYl3zTww", "author_id": "1287068107643719681", "author_username": "ArturoB23022138"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 69}}]}