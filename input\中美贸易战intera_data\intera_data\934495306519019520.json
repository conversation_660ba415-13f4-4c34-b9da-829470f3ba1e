{"user_id": "934495306519019520", "interactions_count": 23, "interactions": [{"conversation_id": "1886052616523911622", "tweet_id": "1886071565772525640", "timestamp": "2025-02-02T15:17:57+00:00", "timestamp_unix": 1738509477, "type": "comment", "text": "@Ryan_Fish @TrumpDailyPosts It’s gonna hurt a lot longer than that.  Consumers on both sides are about to see some serious price increases", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 135}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886071695326208024", "timestamp": "2025-02-02T15:18:28+00:00", "timestamp_unix": 1738509508, "type": "comment", "text": "@Amyo269Ao @TrumpDailyPosts It’s gonna be an expensive 4 years", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 54}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886071941569675352", "timestamp": "2025-02-02T15:19:27+00:00", "timestamp_unix": 1738509567, "type": "comment", "text": "@NationalConAmer @TrumpDailyPosts Consumers on both sides are about to get hammered.  Cheers", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 73}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886074627887186285", "timestamp": "2025-02-02T15:30:07+00:00", "timestamp_unix": 1738510207, "type": "comment", "text": "@ThatJewishLady @TrumpDailyPosts As long as one of the things wasn’t cheaper good’s because those days are gone", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 3, "quote_count": 0, "view_count": 284}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886074800256249986", "timestamp": "2025-02-02T15:30:48+00:00", "timestamp_unix": 1738510248, "type": "comment", "text": "@IAMGDB @TrumpDailyPosts Gonna be an expensive 4 years, cheers", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886075181241700368", "timestamp": "2025-02-02T15:32:19+00:00", "timestamp_unix": 1738510339, "type": "comment", "text": "@ScottDorton1 @IndiGenBharat @TrumpDailyPosts Pullout the wallet because shit is going to get expensive, cheers", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 137}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886075365191229472", "timestamp": "2025-02-02T15:33:03+00:00", "timestamp_unix": 1738510383, "type": "comment", "text": "@zaida_alicea @TrumpDailyPosts Gonna be an expensive 4 years, cheers", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886079325360844809", "timestamp": "2025-02-02T15:48:47+00:00", "timestamp_unix": 1738511327, "type": "comment", "text": "@princessTCSD @Amyo269Ao @TrumpDailyPosts Awe is someone easily triggered.  Sorry your emotions control you maam.  Have a great day", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886079884063076694", "timestamp": "2025-02-02T15:51:00+00:00", "timestamp_unix": 1738511460, "type": "comment", "text": "@LadyMegz92 @hashirsher_ca @TrumpDailyPosts Damn she’s a softie blocked for pointing out the obvious.  Enjoy your prices grumpy", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886080629609050344", "timestamp": "2025-02-02T15:53:58+00:00", "timestamp_unix": 1738511638, "type": "comment", "text": "@og_manga @hashirsher_ca @TrumpDailyPosts Like last time they tried https://t.co/igVMC5XNyE", "context": {"type": "tweet", "id": "1909992723915866311", "text": "\"Capitalization rules\" are a recent innovation which I wouldn't mourn.\n\nTraditionally, written English uses capital letters pretty randomly to denote words of structural or meaningful importance. We'd also sprinkle in all caps, small caps, etc. Retvrn to vibe capitalization. https://t.co/XkuhBDrnTD", "author_id": "1046794373764194307", "author_username": "atlanticesque"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886104223026143380", "timestamp": "2025-02-02T17:27:43+00:00", "timestamp_unix": 1738517263, "type": "comment", "text": "@sthrnmoods @hashirsher_ca @TrumpDailyPosts Great so one of us gets punched in the face the other gets kicked in the nuts.  Great for all of the people on both sides of the border.  We’re both just gonna pay more and <PERSON><PERSON><PERSON> and <PERSON> will be unaffected.  Good thing <PERSON> didn’t run on lowering prices.  Cheers", "context": {"type": "tweet", "id": "1886080345272987780", "text": "@WpgSpidey @hashirsher_ca @TrumpDailyPosts It will hurt Canada way more than it does us. Bank on it. We don't need nothing Canada has we can live without any of it &amp; do just fine. Canada needs us.  Cheers", "author_id": "*********", "author_username": "sthrnmoods"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886110023547277572", "timestamp": "2025-02-02T17:50:46+00:00", "timestamp_unix": **********, "type": "comment", "text": "@sthrnmoods @hashirsher_ca @TrumpDailyPosts It’s not a battle between countries it’s a trade war, do you really think prices are not gonna go up?  Also who did you defend us from?  Just wait and see, cheers", "context": {"type": "tweet", "id": "1886105091624636708", "text": "@WpgSpidey @hashirsher_ca @TrumpDailyPosts Nah As I said Canada is going to lose the battle. Nothing we get from Canada is a must have or needed. We voted for just what <PERSON> is doing hard concept I am sure but it's cold hard truth. I guess you guys will have to find someone else to defend yall too. Cheers", "author_id": "*********", "author_username": "sthrnmoods"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886114187941679192", "timestamp": "2025-02-02T18:07:19+00:00", "timestamp_unix": 1738519639, "type": "comment", "text": "@sthrnmoods @hashirsher_ca @TrumpDailyPosts Great you guys are dating good for you", "context": {"type": "tweet", "id": "1886110815922651416", "text": "@WpgSpidey @hashirsher_ca @TrumpDailyPosts Cheers! 👇👇👇👇", "author_id": "*********", "author_username": "sthrnmoods"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886117170557403568", "timestamp": "2025-02-02T18:19:10+00:00", "timestamp_unix": 1738520350, "type": "comment", "text": "@sthrnmoods @hashirsher_ca @TrumpDailyPosts Who did you defend us from?", "context": {"type": "tweet", "id": "1886114504636821777", "text": "@WpgSpidey @hashirsher_ca @TrumpDailyPosts lol", "author_id": "*********", "author_username": "sthrnmoods"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886122516713808370", "timestamp": "2025-02-02T18:40:25+00:00", "timestamp_unix": 1738521625, "type": "comment", "text": "@sthrnmoods @hashirsher_ca @TrumpDailyPosts Im conservative, thanks for proving my point you defended us from no one.   Not pissed at all actually, easy to see in my feed I was going for Trump.  Not surprised you responded this way.  Your education is shit in America.  Cheers", "context": {"type": "tweet", "id": "1886118153584795719", "text": "@WpgSpidey @hashirsher_ca @TrumpDailyPosts get in a war &amp; find out. You <PERSON> are so pissed that all Trump asked for was to stop illegals from flooding in to our country &amp; Fentanyl to stop. Instead Canada preferred tariff war thats going to cost bigly! I love the fact you are bitching &amp; moaning about the outcome.", "author_id": "*********", "author_username": "sthrnmoods"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1886052616523911622", "tweet_id": "1886126568256241680", "timestamp": "2025-02-02T18:56:31+00:00", "timestamp_unix": 1738522591, "type": "comment", "text": "@sthrnmoods @hashirsher_ca @TrumpDailyPosts Cower and hide, not shocked.  That’s another W", "context": {"type": "tweet", "id": "1886122516713808370", "text": "@sthrnmoods @hashirsher_ca @TrumpDailyPosts Im conservative, thanks for proving my point you defended us from no one.   Not pissed at all actually, easy to see in my feed I was going for Trump.  Not surprised you responded this way.  Your education is shit in America.  Cheers", "author_id": "934495306519019520", "author_username": "WpgSpidey"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1910861833331937521", "tweet_id": "1911133157807849502", "timestamp": "2025-04-12T19:03:46+00:00", "timestamp_unix": 1744484626, "type": "comment", "text": "@johnjames1131 @RetroAgent12 Absolutely shocking they got you to pay for a free service, lmao", "context": {"type": "tweet", "id": "1920007186173833586", "text": "@123<PERSON>ani @<PERSON>lovanov \"<PERSON> ain't no fool\"\n\nThat's your argument? 😆\nTheir evidence  might actually have a basis in fact!\nhttps://t.co/bLCjJSsfdv", "author_id": "197734408", "author_username": "twoeyedloon"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 80}}, {"conversation_id": "1912215585217294494", "tweet_id": "1912560771411898597", "timestamp": "2025-04-16T17:36:36+00:00", "timestamp_unix": 1744824996, "type": "comment", "text": "@EricLDaugh Lmao, 🇨🇦 https://t.co/gHHIF8L7SZ", "context": {"type": "tweet", "id": "1915145225259147631", "text": "Chinese puff up their cultural heritage to make themselves feel better about themselves. Biblically speaking, the older civilizations trace back to the confusion of tongues and the dispersion (Gen. 10), so the Chinese are as old as the rest of us.", "author_id": "1563606991062573056", "author_username": "GreenDuck777"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 2}}, {"conversation_id": "1912215585217294494", "tweet_id": "1912560925686788604", "timestamp": "2025-04-16T17:37:13+00:00", "timestamp_unix": 1744825033, "type": "comment", "text": "@KingLagoswa @EricLDaugh Lmao, shocked they were able to get you to pay for a free service.  Hilarious", "context": {"type": "tweet", "id": "1915145225259147631", "text": "Chinese puff up their cultural heritage to make themselves feel better about themselves. Biblically speaking, the older civilizations trace back to the confusion of tongues and the dispersion (Gen. 10), so the Chinese are as old as the rest of us.", "author_id": "1563606991062573056", "author_username": "GreenDuck777"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 5}}, {"conversation_id": "1912215585217294494", "tweet_id": "1912561354801885530", "timestamp": "2025-04-16T17:38:55+00:00", "timestamp_unix": 1744825135, "type": "comment", "text": "@flexinballs @pharmabroo @scharfmel @EricLDaugh Lmao, and the epstein list is coming too, sucker!!", "context": {"type": "tweet", "id": "1912556773967556915", "text": "@pharmabroo @scharfmel @EricLDaugh • Negotiations in Progress: Over 75 countries have reached out to the White House to negotiate, with active discussions involving major trading partners like Japan, South Korea, Vietnam, India, and the European Union.", "author_id": "385250320", "author_username": "flexinballs"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 32}}, {"conversation_id": "1912215585217294494", "tweet_id": "1912561935788438008", "timestamp": "2025-04-16T17:41:13+00:00", "timestamp_unix": 1744825273, "type": "comment", "text": "@InsidiousTeaCup @pharmabroo @EricLDaugh Of course and the epstein list is gonna be released too, lmao", "context": {"type": "tweet", "id": "1912538212335259986", "text": "@pharmabroo @EricLDaugh Sure, if you rely on the media to actually keep you informed.\n\nOtherwise you'd know up to 70 countries or more already came to the table to remove their own tariffs.", "author_id": "2777131320", "author_username": "InsidiousTeaCup"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 7}}, {"conversation_id": "1912215585217294494", "tweet_id": "1912562047629603040", "timestamp": "2025-04-16T17:41:40+00:00", "timestamp_unix": 1744825300, "type": "comment", "text": "@InsidiousTeaCup @pharmabroo @EricLDaugh There were no tariffs it was based on a trade deficit, so stupid", "context": {"type": "tweet", "id": "1912546512518934863", "text": "@pharmabroo @Eric<PERSON>augh I phrased it wrong. They made a deal in the US's favor and removed their tariffs.", "author_id": "2777131320", "author_username": "InsidiousTeaCup"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1912215585217294494", "tweet_id": "1912589785417609275", "timestamp": "2025-04-16T19:31:53+00:00", "timestamp_unix": 1744831913, "type": "comment", "text": "@flexinballs @pharmabroo @scharfmel @EricLDaugh https://t.co/RvpqchyRgG", "context": {"type": "tweet", "id": "1915145225259147631", "text": "Chinese puff up their cultural heritage to make themselves feel better about themselves. Biblically speaking, the older civilizations trace back to the confusion of tongues and the dispersion (Gen. 10), so the Chinese are as old as the rest of us.", "author_id": "1563606991062573056", "author_username": "GreenDuck777"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 7}}]}