{"user_id": "1874801013858930688", "interactions_count": 135, "interactions": [{"conversation_id": "1921832491339465198", "tweet_id": "1921834246928314409", "timestamp": "2025-05-12T07:46:05+00:00", "timestamp_unix": 1747035965, "type": "comment", "text": "@zaobaosg 习主席不能跪啊，必须单方面吊打", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 46, "like_count": 9, "quote_count": 0, "view_count": 9841}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921841345074700431", "timestamp": "2025-05-12T08:14:17+00:00", "timestamp_unix": 1747037657, "type": "comment", "text": "@davidmoses1995 @zaobaosg 习主席减了百分之105，川普减了百分之106，试问小🕷️，谁先减到百分之百", "context": {"type": "tweet", "id": "1921840484822966366", "text": "@zhongnanhaihu5 @zaobaosg 你眼瞎了？川普都亲中国的ass了，都开始跪舔了，你看不见？", "author_id": "1828344305678983171", "author_username": "davidmoses1995"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 618}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921847051190435983", "timestamp": "2025-05-12T08:36:57+00:00", "timestamp_unix": 1747039017, "type": "comment", "text": "@birdyi23 @zaobaosg 中国是猪承担的吗", "context": {"type": "tweet", "id": "1921845892228792427", "text": "@zhongnanhaihu5 @zaobaosg 别忘了那30%的关税是美国人承担的", "author_id": "1629685718082813952", "author_username": "birdyi23"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 2, "quote_count": 0, "view_count": 793}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921847166068248958", "timestamp": "2025-05-12T08:37:25+00:00", "timestamp_unix": 1747039045, "type": "comment", "text": "@EileenM71244655 @zaobaosg 美国的目的是什么，让蜘蛛们都失业吗", "context": {"type": "tweet", "id": "1921844185004417272", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹确实跪了，没达到目的就回到了原点。", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 2, "quote_count": 0, "view_count": 698}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921847532012896350", "timestamp": "2025-05-12T08:38:52+00:00", "timestamp_unix": 1747039132, "type": "comment", "text": "@liyifan21126 @zaobaosg 你是蜘蛛不接受反驳", "context": {"type": "tweet", "id": "1921845162247950806", "text": "@zhongnanhaihu5 @zaobaosg 你是二逼不接受反驳😆", "author_id": "1833004920620072960", "author_username": "liyifan21126"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 329}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921849273542455747", "timestamp": "2025-05-12T08:45:47+00:00", "timestamp_unix": 1747039547, "type": "comment", "text": "@EileenM71244655 @zaobaosg 那为什么支那也减了？", "context": {"type": "tweet", "id": "1921848848646893652", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹不是要制造业回流么，继续加关税啊，怂啥", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 122}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921849711310377341", "timestamp": "2025-05-12T08:47:32+00:00", "timestamp_unix": 1747039652, "type": "comment", "text": "@chadiamwhy @davidmoses1995 @zaobaosg 哪里来的浮动，这就叫做赢了习主席舔皮炎见不得光", "context": {"type": "tweet", "id": "1921847494104813993", "text": "@zhongnanhaihu5 @davidmoses1995 @zaobaosg 傻逼 美对中是30% 中对美是10-30%浮动 你是没脑子在这瞎几把乱喷粪", "author_id": "1542426974278369280", "author_username": "chadiamwhy"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 129}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921849968135926173", "timestamp": "2025-05-12T08:48:33+00:00", "timestamp_unix": 1747039713, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你有本事不减啊，你减了是心虚啥", "context": {"type": "tweet", "id": "1921849597825094125", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹减少了，中国当然也减少啊。\n中国又不是发起贸易战的一方，是你美国爹发起的", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 132}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921851605646754272", "timestamp": "2025-05-12T08:55:03+00:00", "timestamp_unix": 1747040103, "type": "comment", "text": "@EileenM71244655 @zaobaosg 那你共爹怎么减那么多，还有你怎么知道美国不是为了消除贸易壁垒，你当川普是小学生🤣", "context": {"type": "tweet", "id": "1921850125405561085", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹有本事别减少啊，怂啥？不是你美国爹开始的么", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 54}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921851824744640532", "timestamp": "2025-05-12T08:55:55+00:00", "timestamp_unix": 1747040155, "type": "comment", "text": "@AmyJohn92525208 @EileenM71244655 @zaobaosg 投资叫送台积电，你咋不说你妈去人家是去接客🤣", "context": {"type": "tweet", "id": "1921851441045536919", "text": "@zhongnanhaihu5 @EileenM71244655 @zaobaosg 因为等不到电话，没办法，蛙蛙送台积电送钱，连上桌的机会都没有😂", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 76}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921851992869052459", "timestamp": "2025-05-12T08:56:36+00:00", "timestamp_unix": 1747040196, "type": "comment", "text": "@9XXbmWVZDrX9KnK @zaobaosg 🕷️", "context": {"type": "tweet", "id": "1921851729076711854", "text": "@zhongnanhaihu5 @zaobaosg 哈哈，看傻逼", "author_id": "1439942570067435520", "author_username": "9XXbmWVZDrX9KnK"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 467}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921852969391124625", "timestamp": "2025-05-12T09:00:28+00:00", "timestamp_unix": 1747040428, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你怎么知道不是支那跪舔美爹谈判的，毕竟那么多支那人等着美爹给的饭碗活呢🤭", "context": {"type": "tweet", "id": "1921852538342523338", "text": "@zhongnanhaihu5 @zaobaosg 那你继续加啊，怂啥，你美国爹发起的贸易战自己顶不住找中国谈又降低干嘛", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 25}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921853566567780439", "timestamp": "2025-05-12T09:02:51+00:00", "timestamp_unix": 1747040571, "type": "comment", "text": "@lxdehao91 @birdyi23 @zaobaosg 昨天猪圈里被大黑拱的是你木吗？", "context": {"type": "tweet", "id": "1921853173448290410", "text": "@zhongnanhaihu5 @birdyi23 @zaobaosg 还是你妈承担，回去看看你妈是不是在和你爷爷操逼。", "author_id": "976305935244079104", "author_username": "lxdehao91"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 192}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921854049114014169", "timestamp": "2025-05-12T09:04:46+00:00", "timestamp_unix": 1747040686, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你共匪爹为了稳住🕷️心，主动消除贸易壁垒，消减原来高额对美关税，还想洗小🕷️🤣", "context": {"type": "tweet", "id": "1921853687959564716", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹自己发动贸易战加到145，结果顶不住又自己降低到了30，笑死了，怂狗，继续加啊，", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921854177304535213", "timestamp": "2025-05-12T09:05:16+00:00", "timestamp_unix": 1747040716, "type": "comment", "text": "@Father_fucky @AmyJohn92525208 @EileenM71244655 @zaobaosg 那昨晚我点的是不是你木🤭", "context": {"type": "tweet", "id": "1921853638831489158", "text": "@zhongnanhaihu5 @AmyJohn92525208 @EileenM71244655 @zaobaosg 你妈确实去接客了，还是拜登嫖的你妈", "author_id": "1815794884533723136", "author_username": "Father_fucky"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 28}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921854674795192682", "timestamp": "2025-05-12T09:07:15+00:00", "timestamp_unix": 1747040835, "type": "comment", "text": "@EileenM71244655 @zaobaosg 低端制造业谁要，你共匪爹造的出3mm芯片吗🤣", "context": {"type": "tweet", "id": "1921854183239524524", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹好怂，继续加啊，谈判干嘛啊，制造业回流不要了吗😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921854732924076495", "timestamp": "2025-05-12T09:07:29+00:00", "timestamp_unix": 1747040849, "type": "comment", "text": "@elk0809 @ql_nds8gdc @zaobaosg 看不起农村人？", "context": {"type": "tweet", "id": "1921854448789340179", "text": "@ql_nds8gdc @zhongnanhaihu5 @zaobaosg 来对地方了孩子，这傻逼刘志鵬像极了那种守村人😂", "author_id": "1899280571563651072", "author_username": "elk0809"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921855709651550238", "timestamp": "2025-05-12T09:11:22+00:00", "timestamp_unix": 1747041082, "type": "comment", "text": "@EileenM71244655 @zaobaosg 🕷️你咋不让共匪爹加呢，你不知道原来支美之间关税多不对等吗，支那对美降低这么多关税不是更有利于美国制造业🤡", "context": {"type": "tweet", "id": "1921854852063273282", "text": "@zhongnanhaihu5 @zaobaosg 那你让你美国爹继续加啊，你美国爹说过要制造业需要，怂啥啊😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921855997296918545", "timestamp": "2025-05-12T09:12:30+00:00", "timestamp_unix": 1747041150, "type": "comment", "text": "@Charles201409 @zaobaosg 搞得你多帅似的，敢露脸瞅你那怂逼样子吗🤣", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 98}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921856188666274304", "timestamp": "2025-05-12T09:13:16+00:00", "timestamp_unix": 1747041196, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你共匪爹答应减的关税，有本事一直维持原来的对等关税啊😂", "context": {"type": "tweet", "id": "1921855902665081233", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹发起的贸易战，又不是中国发起的，你让你美国爹继续啊，怂啥。😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921856296577347703", "timestamp": "2025-05-12T09:13:42+00:00", "timestamp_unix": 1747041222, "type": "comment", "text": "@Sank362767403 @zaobaosg 搞得你多帅似的，见的了光吗？", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921856722794103028", "timestamp": "2025-05-12T09:15:23+00:00", "timestamp_unix": 1747041323, "type": "comment", "text": "@Charles201409 @zaobaosg 我吓到睡谁了，吓到你木了对吧😂", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921856995763617996", "timestamp": "2025-05-12T09:16:28+00:00", "timestamp_unix": 1747041388, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你咋知道你美国先答应的，不是贵支先在底下疯狂求吗，不然也不会削减关税，当然墙国有新闻管制，不能让🕷️们看见习主席舔皮炎😂", "context": {"type": "tweet", "id": "1921856501225865582", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹答应减税，有啥好答应的啊，不是你美国爹发起的吗？怂啥😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921858734667542962", "timestamp": "2025-05-12T09:23:23+00:00", "timestamp_unix": 1747041803, "type": "comment", "text": "@EileenM71244655 @zaobaosg 但是最后呢，贸易壁垒被消除了，你不会以为支那赢了吧😂", "context": {"type": "tweet", "id": "1921857290702913554", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹喊中国来谈判的，又不是中国主动的。你美国爹发起的贸易战，不是中国发起的。怂啥啊，继续加啊，有什么好降低的😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921858888996954357", "timestamp": "2025-05-12T09:24:00+00:00", "timestamp_unix": 1747041840, "type": "comment", "text": "@TWFrogKiller @zaobaosg 你有自信拿你爹当头像干吗😂", "context": {"type": "tweet", "id": "1921858554190877076", "text": "@zhongnanhaihu5 @zaobaosg 你是有多么自信还把自拍当头像🤣", "author_id": "1128918130682605568", "author_username": "TWFrogKiller"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 239}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921859214713963005", "timestamp": "2025-05-12T09:25:17+00:00", "timestamp_unix": 1747041917, "type": "comment", "text": "@Bafuxunan666 @zaobaosg 那你的发言有什么意义呢，是想证明什么", "context": {"type": "tweet", "id": "1921858643391127786", "text": "@zhongnanhaihu5 @zaobaosg 看到那么多人骂你这个智障，老夫善心大发，不参与群殴傻逼了。", "author_id": "1758894221707407360", "author_username": "Bafuxunan666"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 239}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921859400106479717", "timestamp": "2025-05-12T09:26:02+00:00", "timestamp_unix": 1747041962, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你支那好像没减似的，看看谁对比原先减的多好吧🤣", "context": {"type": "tweet", "id": "1921858921116893512", "text": "@zhongnanhaihu5 @zaobaosg 最后就是你美国爹顶不住怂了不得不降低关税啊😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921863105639821477", "timestamp": "2025-05-12T09:40:45+00:00", "timestamp_unix": 1747042845, "type": "comment", "text": "@EileenM71244655 @zaobaosg 但是目的达到了呀，这就是川普的智慧，小学生永远不可能懂的啊🤣", "context": {"type": "tweet", "id": "1921859659046044147", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹减中国就减，毕竟你美国爹发起的贸易战，不是中国发起的。继续啊，让你美国爹别减少😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921863232173613098", "timestamp": "2025-05-12T09:41:15+00:00", "timestamp_unix": 1747042875, "type": "comment", "text": "@Bafuxunan666 @zaobaosg 全世界指的是猪圈吗？", "context": {"type": "tweet", "id": "1921859432700400015", "text": "@zhongnanhaihu5 @zaobaosg 证明全世界都知道你是傻逼，只有你自己不知道。", "author_id": "1758894221707407360", "author_username": "Bafuxunan666"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921863818138812436", "timestamp": "2025-05-12T09:43:35+00:00", "timestamp_unix": 1747043015, "type": "comment", "text": "@lemo_n_777 哎，湾湾 https://t.co/MB0QLmVQnR", "context": {"type": "tweet", "id": "1921863705148403950", "text": "@zhongnanhaihu5 弯弯不是先跪下的嘛哈哈哈🤣每天都能看到🐸又破防啦哈哈哈", "author_id": "1632058616", "author_username": "lemo_n_777"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 234}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921865141387206805", "timestamp": "2025-05-12T09:48:50+00:00", "timestamp_unix": 1747043330, "type": "comment", "text": "@lemo_n_777 我急了我发湾湾的图干吗，我是同意你啊😂", "context": {"type": "tweet", "id": "1921864425822187545", "text": "@zhongnanhaihu5 急了？？🐸哈哈哈", "author_id": "1632058616", "author_username": "lemo_n_777"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 57}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921865386850464134", "timestamp": "2025-05-12T09:49:49+00:00", "timestamp_unix": 1747043389, "type": "comment", "text": "@EileenM71244655 @zaobaosg 胡搅蛮缠是吧，究竟谁怂了，贸易壁垒消除看不见，原来中美关税多不对等看不见🤣", "context": {"type": "tweet", "id": "1921863935612838258", "text": "@zhongnanhaihu5 @zaobaosg 让步啥了？你美国爹怂了啊，继续加啊", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 38}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921865444593488224", "timestamp": "2025-05-12T09:50:03+00:00", "timestamp_unix": 1747043403, "type": "comment", "text": "@Lin1985N @zaobaosg 是啊，万猪嘶吼", "context": {"type": "tweet", "id": "1921864348076568690", "text": "@zhongnanhaihu5 @zaobaosg 看大家都在屌你，我就放心了😎", "author_id": "1793668647812251648", "author_username": "Lin1985N"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 3, "quote_count": 0, "view_count": 833}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921865690748702750", "timestamp": "2025-05-12T09:51:01+00:00", "timestamp_unix": 1747043461, "type": "comment", "text": "@w_hula @zaobaosg 说你自己小🕷️", "context": {"type": "tweet", "id": "1921864980862796073", "text": "@zhongnanhaihu5 @zaobaosg 太傻比了😂", "author_id": "1452605055446573058", "author_username": "w_hula"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 780}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921865774806827072", "timestamp": "2025-05-12T09:51:21+00:00", "timestamp_unix": 1747043481, "type": "comment", "text": "@Bafuxunan666 @zaobaosg 支那就是猪圈啊", "context": {"type": "tweet", "id": "1921864125220552791", "text": "@zhongnanhaihu5 @zaobaosg 你的自我介绍在猪圈里是成立的。", "author_id": "1758894221707407360", "author_username": "Bafuxunan666"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921866171789234676", "timestamp": "2025-05-12T09:52:56+00:00", "timestamp_unix": 1747043576, "type": "comment", "text": "@lemo_n_777 送你一张😂 https://t.co/wO6Qc8ezJo", "context": {"type": "tweet", "id": "1921865882940162323", "text": "@zhongnanhaihu5 哈哈哈🤣🐸变红蛙 https://t.co/BxF5eBxXCX", "author_id": "1632058616", "author_username": "lemo_n_777"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 61}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921866674975703244", "timestamp": "2025-05-12T09:54:56+00:00", "timestamp_unix": 1747043696, "type": "comment", "text": "@Natsunagai @zaobaosg 这叫怂，目标已经达成了🤣", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921866743225471220", "timestamp": "2025-05-12T09:55:12+00:00", "timestamp_unix": 1747043712, "type": "comment", "text": "@HK0619451678745 @zaobaosg 还有绿畜🤣", "context": {"type": "tweet", "id": "1921866020970508694", "text": "@zhongnanhaihu5 @zaobaosg 你的怨气很大 来清真寺找我 我让真主安拉把你清真下", "author_id": "1796570961342611456", "author_username": "HK0619451678745"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 203}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921867051947188321", "timestamp": "2025-05-12T09:56:26+00:00", "timestamp_unix": 1747043786, "type": "comment", "text": "@lemo_n_777 日本才是支那的爸爸，不然南京为何能被图30万😂", "context": {"type": "tweet", "id": "1921866803724116134", "text": "@zhongnanhaihu5 赖狗把经费都浪费到你们🐸这些网络喷子身上了吧😪少出来丢人了～中国才是你们的爸爸～🤭少在外面人认野爹😈", "author_id": "1632058616", "author_username": "lemo_n_777"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 59}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921868485530882328", "timestamp": "2025-05-12T10:02:08+00:00", "timestamp_unix": 1747044128, "type": "comment", "text": "@lemo_n_777 我好怕怕😱，你个杨卫男说出这种话不会很讽刺吗🤣", "context": {"type": "tweet", "id": "1921867867252789496", "text": "@zhongnanhaihu5 cnm你在外面到处污蔑你祖宗，等回家爸爸把你的屁股打开花🌼 台湾现在有你们这样的青年，中国也是放心了🤭废物中的废物 败类中的败类，连老祖宗都不认，小心以后断子绝孙哦～", "author_id": "1632058616", "author_username": "lemo_n_777"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 53}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921868596759654576", "timestamp": "2025-05-12T10:02:34+00:00", "timestamp_unix": 1747044154, "type": "comment", "text": "@Bafuxunan666 @zaobaosg china 跟我读 chi +na chi na chi na", "context": {"type": "tweet", "id": "1921867570568630678", "text": "@zhongnanhaihu5 @zaobaosg 好吧，你赢了。 https://t.co/FRp0hTHUKv", "author_id": "1758894221707407360", "author_username": "Bafuxunan666"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921869368683589736", "timestamp": "2025-05-12T10:05:38+00:00", "timestamp_unix": 1747044338, "type": "comment", "text": "@EileenM71244655 @zaobaosg 那你共匪爹怂啥，有本事别减啊🤣", "context": {"type": "tweet", "id": "1921869017846882506", "text": "@zhongnanhaihu5 @zaobaosg 胡搅蛮缠的是你，你美国爹怂啥？😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 4}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921869498379825294", "timestamp": "2025-05-12T10:06:09+00:00", "timestamp_unix": 1747044369, "type": "comment", "text": "@lemo_n_777 我好怕怕😱，你过来啊，别当键盘侠啊🤣", "context": {"type": "tweet", "id": "1921868870886785166", "text": "@zhongnanhaihu5 🤭像你这种败类，第一个就先被干掉吧哈哈哈", "author_id": "1632058616", "author_username": "lemo_n_777"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 41}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921869934956585402", "timestamp": "2025-05-12T10:07:53+00:00", "timestamp_unix": 1747044473, "type": "comment", "text": "@EileenM71244655 @zaobaosg 那你看支那都减成这样，是不是支那主动献上皮炎🤣", "context": {"type": "tweet", "id": "1921869473302094177", "text": "@zhongnanhaihu5 @zaobaosg 肯定是你美国爹怂啊，发起贸易战的是你美国爹，拜登时期就30%，你川普爹得到啥了？一圈回来还是30😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921870055911911634", "timestamp": "2025-05-12T10:08:22+00:00", "timestamp_unix": 1747044502, "type": "comment", "text": "@EileenM71244655 @zaobaosg 那你支爹怂啥，不要减啊🤣", "context": {"type": "tweet", "id": "1921869538787819773", "text": "@zhongnanhaihu5 @zaobaosg 那你美国爹怂啥？继续加啊，😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921870139743482292", "timestamp": "2025-05-12T10:08:42+00:00", "timestamp_unix": 1747044522, "type": "comment", "text": "@lemo_n_777 我好怕怕解放军兵哥哥🤣", "context": {"type": "tweet", "id": "1921869745810211279", "text": "@zhongnanhaihu5 哈哈哈🤣放心放心～快啦弱鸡草莓兵", "author_id": "1632058616", "author_username": "lemo_n_777"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 93}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921870886484804014", "timestamp": "2025-05-12T10:11:40+00:00", "timestamp_unix": 1747044700, "type": "comment", "text": "@EileenM71244655 @zaobaosg 那你别让支爹减到百分之十啊🤣", "context": {"type": "tweet", "id": "1921870064862568939", "text": "@zhongnanhaihu5 @zaobaosg 那你倒是别让你美国爹减啊，继续啊，怂啥，不是你美国爹发起的贸易战吗😂", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921873095003922771", "timestamp": "2025-05-12T10:20:27+00:00", "timestamp_unix": 1747045227, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你当川普是小学生吗，支那肯定给了美爹更多，不然能签协议🤣", "context": {"type": "tweet", "id": "1921871419228492190", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹减了中国当让也减啊，贸易战是你美国爹发起的，又不是中国发起的。你美国爹继续啊，怂啥呢🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921873202461995437", "timestamp": "2025-05-12T10:20:52+00:00", "timestamp_unix": 1747045252, "type": "comment", "text": "@eil45794574 @AmyJohn92525208 @EileenM71244655 @zaobaosg 大本营，你是从哪儿看来的新闻🤣", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921874126265827369", "timestamp": "2025-05-12T10:24:33+00:00", "timestamp_unix": 1747045473, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你习近平爹认怂了，偷偷豁免关税示好，然后派人前去谈判，还减回去关税，还要解决芬太尼问题讨好美爹🤣", "context": {"type": "tweet", "id": "1921873367856009489", "text": "@zhongnanhaihu5 @zaobaosg 你川普爹认怂了，开打前30，现在还30，怎么不继续呢🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 74}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921874656505503809", "timestamp": "2025-05-12T10:26:39+00:00", "timestamp_unix": 1747045599, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你咋不让支那别减啊，支那不是天朝上国吗，美国不就是为了消除贸易逆差吗，他的目的已经达到了，你当川普小学生🤣", "context": {"type": "tweet", "id": "1921874203176833396", "text": "@zhongnanhaihu5 @zaobaosg 那你倒是让你美国爹别减啊，继续加啊。怂啥呢？贸易战是你美国爹发起的，加了115，顶不住了又减了115，有啥变化吗？中国没有任何让步，你美国爹得到啥了？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921875259331850243", "timestamp": "2025-05-12T10:29:03+00:00", "timestamp_unix": 1747045743, "type": "comment", "text": "@EileenM71244655 @zaobaosg 这叫交易的艺术，再说只是暂停，要是再加怎么办🤣", "context": {"type": "tweet", "id": "1921874772616495173", "text": "@zhongnanhaihu5 @zaobaosg 你咋不让你美国爹不减呢？贸易战是你美国爹发起的，又不是老钟发起的。继续啊，减啥？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921880236871786655", "timestamp": "2025-05-12T10:48:49+00:00", "timestamp_unix": 1747046929, "type": "comment", "text": "@EileenM71244655 @zaobaosg 什么都没得到，你当川普是小学生，实际上支那肯定给了很多🤣", "context": {"type": "tweet", "id": "1921876919038922871", "text": "@zhongnanhaihu5 @zaobaosg 交易的意淫艺术而已。什么也没得到🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921880348973035676", "timestamp": "2025-05-12T10:49:16+00:00", "timestamp_unix": 1747046956, "type": "comment", "text": "@conycrick @EileenM71244655 @zaobaosg 你在说你自己？", "context": {"type": "tweet", "id": "1921876744304312644", "text": "@zhongnanhaihu5 @EileenM71244655 @zaobaosg 我看出来了，你就是一个大傻逼，哈哈！", "author_id": "1114548837345976322", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921880715915858030", "timestamp": "2025-05-12T10:50:44+00:00", "timestamp_unix": 1747047044, "type": "comment", "text": "@s5D7TYXFnEXOHg8 @HuangZu02699 @zaobaosg 还不是支那偷偷先豁免某些关税然后去求美爹答应什么芬太尼消除贸易逆差之类的，当然这都是暂时的或许几个月后美国又增加呢🤣", "context": {"type": "tweet", "id": "1921877013897338950", "text": "@zhongnanhaihu5 @HuangZu02699 @zaobaosg 关税是美国加的，中国报复，然后美国就减回去了，中国也减了，你说是谁跪了？", "author_id": "1301308754453958656", "author_username": "s5D7TYXFnEXOHg8"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921880830881767785", "timestamp": "2025-05-12T10:51:11+00:00", "timestamp_unix": 1747047071, "type": "comment", "text": "@HollyHollo31469 @zaobaosg 照照镜子再出来叫可以吗？", "context": {"type": "tweet", "id": "1921876855218450851", "text": "@zhongnanhaihu5 @zaobaosg 丑的一逼脑子还残", "author_id": "1661549705200017409", "author_username": "HollyHollo31469"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 53}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921880948137656431", "timestamp": "2025-05-12T10:51:39+00:00", "timestamp_unix": 1747047099, "type": "comment", "text": "@EileenM71244655 @zaobaosg 得到啥，我还要再说吗，得到你木了对吧🤣", "context": {"type": "tweet", "id": "1921880574437822959", "text": "@zhongnanhaihu5 @zaobaosg 是啊，啥也没得到，你 川普爹得到啥了？🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 8}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921881126315909499", "timestamp": "2025-05-12T10:52:21+00:00", "timestamp_unix": 1747047141, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 小🕷️胡搅蛮缠倒是可以的🤣", "context": {"type": "tweet", "id": "1921880638019297488", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 说的就是你这弱智反贼🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 12}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921881528449028274", "timestamp": "2025-05-12T10:53:57+00:00", "timestamp_unix": 1747047237, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 小🕷️意淫永远赢麻🤣", "context": {"type": "tweet", "id": "1921881201884709172", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 反贼气炸崩溃，美国爹不争气难受死了🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921882038275121624", "timestamp": "2025-05-12T10:55:59+00:00", "timestamp_unix": 1747047359, "type": "comment", "text": "@EileenM71244655 @zaobaosg 得到啥，得到你木了，除此之外你觉得还有啥🤣", "context": {"type": "tweet", "id": "1921881305865658682", "text": "@zhongnanhaihu5 @zaobaosg 你川普爹得到啥了？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921882676773974293", "timestamp": "2025-05-12T10:58:31+00:00", "timestamp_unix": 1747047511, "type": "comment", "text": "@EileenM71244655 @zaobaosg 得到你木了，行了吧🤣", "context": {"type": "tweet", "id": "1921882273508471174", "text": "@zhongnanhaihu5 @zaobaosg 就知道你说不出得到啥，笑死，之前+115现在-115 啥也米有", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921882827831865384", "timestamp": "2025-05-12T10:59:07+00:00", "timestamp_unix": 1747047547, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 对对对，美爹一场空，签个协议就是为了跪舔🤣", "context": {"type": "tweet", "id": "1921882164745953335", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 反贼猪急也没用，美国爹不争气🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921882884278825323", "timestamp": "2025-05-12T10:59:21+00:00", "timestamp_unix": 1747047561, "type": "comment", "text": "@HollyHollo31469 @zaobaosg 你咋不发个毛出来🤣", "context": {"type": "tweet", "id": "1921882521584676902", "text": "@zhongnanhaihu5 @zaobaosg 不用照，你连我一根阴毛都不如", "author_id": "1661549705200017409", "author_username": "HollyHollo31469"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921883806434222477", "timestamp": "2025-05-12T11:03:00+00:00", "timestamp_unix": 1747047780, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 美爹弄不过支那，那支那做贼心虚似的偷偷去求谈判什么意思🤣", "context": {"type": "tweet", "id": "1921883474014634372", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 是的，你气也没用，谁叫你美国爹弄不动老钟呢", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921884126900048131", "timestamp": "2025-05-12T11:04:17+00:00", "timestamp_unix": 1747047857, "type": "comment", "text": "@EileenM71244655 @zaobaosg 小🕷️又在幻想了，明明是支那先偷偷豁免关税示好然后前去谈判答应解决芬太尼以及消除贸易逆差的问题🤣", "context": {"type": "tweet", "id": "1921883410840064416", "text": "@zhongnanhaihu5 @zaobaosg 胡搅蛮缠的不是你么，你美国爹不争气怪谁呢🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921884214254792713", "timestamp": "2025-05-12T11:04:38+00:00", "timestamp_unix": 1747047878, "type": "comment", "text": "@HollyHollo31469 @zaobaosg 你不会没有吧🤣", "context": {"type": "tweet", "id": "1921883881944351115", "text": "@zhongnanhaihu5 @zaobaosg 因为你不配看阿", "author_id": "1661549705200017409", "author_username": "HollyHollo31469"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 19}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921884465518829759", "timestamp": "2025-05-12T11:05:38+00:00", "timestamp_unix": 1747047938, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 小🕷️精神胜利是吧，明明是支那先怂的，有本事不要减啊，不要答应各种美爹的要求啊，继续打啊🤣", "context": {"type": "tweet", "id": "1921883921303622051", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 确实拧不过，没办法，谁叫你美国爹认怂呢，继续加啊🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921885483015004195", "timestamp": "2025-05-12T11:09:40+00:00", "timestamp_unix": 1747048180, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 目的是什么，难道川普脑子跟你一样🤣", "context": {"type": "tweet", "id": "1921884811246911879", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 你美国爹先怂，毕竟贸易战是你美国爹发起的，又不是老钟发起的，没达到目的就顶不住了🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921885617786323240", "timestamp": "2025-05-12T11:10:12+00:00", "timestamp_unix": 1747048212, "type": "comment", "text": "@EileenM71244655 @zaobaosg 明明是支那先找美国谈判的，你有本事别谈判啊🤣", "context": {"type": "tweet", "id": "1921884377593647462", "text": "@zhongnanhaihu5 @zaobaosg 幻想的不是你么，你倒是让你美国爹别找老钟谈判，继续打啊，制造业也么回啊，减啥？贸易战开大前就30 现在还30🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921886407531929676", "timestamp": "2025-05-12T11:13:21+00:00", "timestamp_unix": 1747048401, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你支那先豁免关税讨好然后私下偷偷去讨好，不知道吗小🕷️🤣", "context": {"type": "tweet", "id": "1921885858057040292", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹发起的贸易战，你美国爹先找的老钟🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921886469196587186", "timestamp": "2025-05-12T11:13:35+00:00", "timestamp_unix": 1747048415, "type": "comment", "text": "@owlnight2000 @Lin1985N @zaobaosg 不是你先照吗🤣", "context": {"type": "tweet", "id": "1921886189574856740", "text": "@zhongnanhaihu5 @Lin1985N @zaobaosg 大概率只有一头，你照照镜子?", "author_id": "1494550290485284869", "author_username": "owlnight2000"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 66}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921886763980620276", "timestamp": "2025-05-12T11:14:46+00:00", "timestamp_unix": 1747048486, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 你支那爹怂啥，继续加啊，韭菜不管了吗，啊🤣🤣", "context": {"type": "tweet", "id": "1921886364959670411", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 你美国爹怂啥，继续加啊，制造业不要了吗，啊🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921887143909073402", "timestamp": "2025-05-12T11:16:16+00:00", "timestamp_unix": 1747048576, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你支那偷偷豁免关税，怂什么怂什么🤣", "context": {"type": "tweet", "id": "1921886780099264756", "text": "@zhongnanhaihu5 @zaobaosg 你美发动的贸易战，继续加啊，减啥？怂啥", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 11}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921887602786890043", "timestamp": "2025-05-12T11:18:06+00:00", "timestamp_unix": 1747048686, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 那你支那豁免关税认怂干吗，还不是想讨好你美爹🤣", "context": {"type": "tweet", "id": "1921887171474059615", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 你美国爹怂啥?  发动贸易战的是你美国爹，又不是老钟。没达到目的怎么就减了 呢？ 之前30现在还30，怎么回事啊|？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921887800128909684", "timestamp": "2025-05-12T11:18:53+00:00", "timestamp_unix": 1747048733, "type": "comment", "text": "@EileenM71244655 @zaobaosg 你支那之前10现在还10，怎么回事啊🤣", "context": {"type": "tweet", "id": "1921887297714233504", "text": "@zhongnanhaihu5 @zaobaosg 你美国爹之前30现在还30，怎么回事啊？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 6}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921888002323743153", "timestamp": "2025-05-12T11:19:41+00:00", "timestamp_unix": 1747048781, "type": "comment", "text": "@owlnight2000 @Lin1985N @zaobaosg 你在找同类吗🤣", "context": {"type": "tweet", "id": "1921887656234856455", "text": "@zhongnanhaihu5 @Lin1985N @zaobaosg 猪在叫谁?猪回话", "author_id": "1494550290485284869", "author_username": "owlnight2000"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 50}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921888078529990673", "timestamp": "2025-05-12T11:19:59+00:00", "timestamp_unix": 1747048799, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 那你支那豁免关税认怂干吗，还不是想讨好你美爹🤣", "context": {"type": "tweet", "id": "1921887791199170907", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 你美国爹发动贸易战的，又不是老钟。没达到目的怎么就减了 呢？ 之前30现在还30，怎么回事啊|？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921888953554132999", "timestamp": "2025-05-12T11:23:28+00:00", "timestamp_unix": 1747049008, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 你支那爹怂啥，继续加啊，韭菜不管了吗，啊🤣🤣", "context": {"type": "tweet", "id": "1921888232767173046", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 那你美国爹认怂谈判干嘛，接着打啊，有啥好谈判的🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921889301161279991", "timestamp": "2025-05-12T11:24:51+00:00", "timestamp_unix": 1747049091, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 那你支那豁免关税认怂干吗，还不是想讨好你美爹🤣", "context": {"type": "tweet", "id": "1921889026782515381", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 你美国爹怂啥?  发动贸易战的是你美国爹，又不是老钟。没达到目的怎么就减了 呢？ 之前30现在还30，怎么回事啊|？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921890085835776114", "timestamp": "2025-05-12T11:27:58+00:00", "timestamp_unix": 1747049278, "type": "comment", "text": "@Abu06296067 @zaobaosg 井底支呱，呱呱呱🐸", "context": {"type": "tweet", "id": "1921889663649730735", "text": "@zhongnanhaihu5 @zaobaosg 看你的面相就像个低能，所以，你别解读这种需要脑子的新闻！好好在井下呆着", "author_id": "1143394177570336769", "author_username": "Abu06296067"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 147}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921890201711858088", "timestamp": "2025-05-12T11:28:25+00:00", "timestamp_unix": 1747049305, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 你咋不让支那别减啊，支那不是天朝上国吗，美国不就是为了消除贸易逆差吗，他的目的已经达到了，你当川普小学生🤣", "context": {"type": "tweet", "id": "1921889671274999884", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 发起贸易战的是你美国爹，又不是老钟，你美国爹为什么不敢继续打了呢？怂啥呢？有什么好减少的？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921890710338388094", "timestamp": "2025-05-12T11:30:27+00:00", "timestamp_unix": 1747049427, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 那你支那豁免关税认怂干吗，还不是想讨好你美爹🤣", "context": {"type": "tweet", "id": "1921890303515955232", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 发起贸易战的是你美国爹，又不是老钟，你美国爹怂啥呢？有什么好减少的？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 26}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921891251214803328", "timestamp": "2025-05-12T11:32:35+00:00", "timestamp_unix": 1747049555, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 支那偷偷给美爹豁免关税并同意打击芬太尼会告诉底层韭菜吗🤣", "context": {"type": "tweet", "id": "1921890854525915211", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 你美国爹怂啥?  发动贸易战的是你美国爹，又不是老钟。没达到目的怎么就减了 呢？ 之前30现在还30，怎么回事啊？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921891874635153804", "timestamp": "2025-05-12T11:35:04+00:00", "timestamp_unix": 1747049704, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 小🕷️精神胜利是吧，明明是支那先怂的，有本事不要减啊，不要答应各种美爹的要求啊，继续打啊🤣", "context": {"type": "tweet", "id": "1921891341123907664", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 发起贸易战的是你美国爹，又不是老钟，你美国爹为什么不敢继续打了呢？怂啥呢？有什么好减少的？🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921891980684013889", "timestamp": "2025-05-12T11:35:29+00:00", "timestamp_unix": 1747049729, "type": "comment", "text": "@Abu06296067 @zaobaosg 我只会说人话🤣", "context": {"type": "tweet", "id": "1921891641788629119", "text": "@zhongnanhaihu5 @zaobaosg 叫的真好听，来来来，再叫几声，哈哈哈哈哈", "author_id": "1143394177570336769", "author_username": "Abu06296067"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921893202564358571", "timestamp": "2025-05-12T11:40:21+00:00", "timestamp_unix": 1747050021, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 胡搅蛮缠的不是你这🕷️吗", "context": {"type": "tweet", "id": "1921892353972838568", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 胡搅蛮缠的不是你ZZ反贼么🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921893329505038467", "timestamp": "2025-05-12T11:40:51+00:00", "timestamp_unix": 1747050051, "type": "comment", "text": "@Abu06296067 @zaobaosg 🕷️真是奇怪的生物", "context": {"type": "tweet", "id": "1921892398180827276", "text": "@zhongnanhaihu5 @zaobaosg 你刚刚不是一直在哇吗？说你面相低能，原来脑子也就这样！能活着呆在台湾省就祈祷吧！别出来作妖了，脑子不够用就放家里别出来！", "author_id": "1143394177570336769", "author_username": "Abu06296067"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921894547786797383", "timestamp": "2025-05-12T11:45:41+00:00", "timestamp_unix": 1747050341, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 确实是你这小🕷️🤣", "context": {"type": "tweet", "id": "1921893350744936508", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 确实是你这ZZ反贼🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921895332255875138", "timestamp": "2025-05-12T11:48:48+00:00", "timestamp_unix": 1747050528, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 确实是你这🕷️🤣", "context": {"type": "tweet", "id": "1921894758902837283", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 确实是你这RZ🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921898186966274552", "timestamp": "2025-05-12T12:00:09+00:00", "timestamp_unix": 1747051209, "type": "comment", "text": "@Sank362767403 @zaobaosg 你对你的颜值有多自信呢", "context": {"type": "tweet", "id": "1921896881241612786", "text": "@zhongnanhaihu5 @zaobaosg 哈哈哈急了，丑逼爱作怪～说的真没错", "author_id": "563831315", "author_username": "Sank362767403"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921900232192712899", "timestamp": "2025-05-12T12:08:17+00:00", "timestamp_unix": 1747051697, "type": "comment", "text": "@bath_leo8769 @zaobaosg 🤡", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 256}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921902863699095564", "timestamp": "2025-05-12T12:18:44+00:00", "timestamp_unix": 1747052324, "type": "comment", "text": "@s5D7TYXFnEXOHg8 @HuangZu02699 @zaobaosg 🧠have否？", "context": {"type": "tweet", "id": "1921901993037087225", "text": "@zhongnanhaihu5 @HuangZu02699 @zaobaosg 那美国不降低关税不就行了呗，不就能一直赢了？为什么要降关税呢？", "author_id": "1301308754453958656", "author_username": "s5D7TYXFnEXOHg8"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921906322766795132", "timestamp": "2025-05-12T12:32:29+00:00", "timestamp_unix": 1747053149, "type": "comment", "text": "@EileenM71244655 @conycrick @zaobaosg 确实是你这🕷️", "context": {"type": "tweet", "id": "1921895496039141532", "text": "@zhongnanhaihu5 @conycrick @zaobaosg 确实是你这反贼ZZ🤣🤣", "author_id": "1531364108846653443", "author_username": "EileenM71244655"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 17}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921907821852905814", "timestamp": "2025-05-12T12:38:26+00:00", "timestamp_unix": 1747053506, "type": "comment", "text": "@s5D7TYXFnEXOHg8 @HuangZu02699 @zaobaosg 回答我，终共无脑韭菜武汉肺炎小粉红，谁是你的父上", "context": {"type": "tweet", "id": "1921907448673108104", "text": "@zhongnanhaihu5 @HuangZu02699 @zaobaosg 回答不出来了？我问你，既然美国加关税是为了打击中国，那现在为什么要降低关税？回答我", "author_id": "1301308754453958656", "author_username": "s5D7TYXFnEXOHg8"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921908804410564701", "timestamp": "2025-05-12T12:42:20+00:00", "timestamp_unix": 1747053740, "type": "comment", "text": "@s5D7TYXFnEXOHg8 @HuangZu02699 @zaobaosg https://t.co/l8WdKLIkYW", "context": {"type": "tweet", "id": "1921908288997707792", "text": "@zhongnanhaihu5 @HuangZu02699 @zaobaosg 废物，别转移话题，我问你特朗普推特说加关税是为了制造业回美国，为什么现在制造业都没有转移就降低关税？答得出来吗废物？答不出来就老老实实当废物，懂吗？", "author_id": "1301308754453958656", "author_username": "s5D7TYXFnEXOHg8"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921909469090320729", "timestamp": "2025-05-12T12:44:59+00:00", "timestamp_unix": 1747053899, "type": "comment", "text": "@s5D7TYXFnEXOHg8 @HuangZu02699 @zaobaosg 回应我，终共无脑韭菜小粉红，你究竟想证明什么呢 https://t.co/oHEbFCIN0U", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 51}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921946079945166918", "timestamp": "2025-05-12T15:10:28+00:00", "timestamp_unix": 1747062628, "type": "comment", "text": "@conycrick @EileenM71244655 @zaobaosg https://t.co/cbo4HiNAYc", "context": {"type": "tweet", "id": "1921945822838636789", "text": "@zhongnanhaihu5 @EileenM71244655 @zaobaosg 说你妈是个臭傻逼", "author_id": "1114548837345976322", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 22}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922077047460937877", "timestamp": "2025-05-12T23:50:53+00:00", "timestamp_unix": 1747093853, "type": "comment", "text": "@AmyJohn92525208 @EileenM71244655 @zaobaosg 韩国三星没有在美国投资，还有三星背后不有华尔街的股份吗", "context": {"type": "tweet", "id": "1921964386513453314", "text": "@zhongnanhaihu5 @EileenM71244655 @zaobaosg 那我不禁要问为什么韩国三星不去美国投资😂说得好听全世界都看明白就你们还在自己骗自己", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 10}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922077333990556066", "timestamp": "2025-05-12T23:52:01+00:00", "timestamp_unix": 1747093921, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 🕷️告诉你吧，在支那跪舔前台湾和美国就达成协议暂停关税了，所以说是你支死鸭子嘴硬撑到最后的", "context": {"type": "tweet", "id": "1921966050150580503", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 哦，还有一件事😂天天说首批谈判名单现在还没上桌啊？32%跪🧎🏻‍♀️最早，后面发现🤡不是全世界看啊共笑话，怎么还没排上我😂", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922078287909564552", "timestamp": "2025-05-12T23:55:48+00:00", "timestamp_unix": 1747094148, "type": "comment", "text": "@14381438c @zaobaosg 搞得你多帅似得，露脸啊", "context": {"type": "tweet", "id": "1922030380720283689", "text": "@zhongnanhaihu5 @zaobaosg 顺便说一句头像是你本人的话，好丑🤣", "author_id": "1833734566080806912", "author_username": "14381438c"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 31}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922078379315994815", "timestamp": "2025-05-12T23:56:10+00:00", "timestamp_unix": 1747094170, "type": "comment", "text": "@xing1202 @HollyHollo31469 @zaobaosg 可怜你没木呢🥹", "context": {"type": "tweet", "id": "1921987894165508125", "text": "@zhongnanhaihu5 @HollyHollo31469 @zaobaosg 都被你妈用嘴拔了", "author_id": "1699954628228796416", "author_username": "xing1202"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921865917241446533", "tweet_id": "1922078481929662846", "timestamp": "2025-05-12T23:56:35+00:00", "timestamp_unix": 1747094195, "type": "comment", "text": "@VWnFsve76nHtKDd @EileenM71244655 @fdsa753159 @bbcchinese 你是不是杨威没得打了😆", "context": {"type": "tweet", "id": "1921992622505152721", "text": "@zhongnanhaihu5 @EileenM71244655 @fdsa753159 @bbcchinese 打完飞机了？别关心这些没用的，继续打", "author_id": "1470354935640621060", "author_username": "VWnFsve76nHtKDd"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922078924256755764", "timestamp": "2025-05-12T23:58:20+00:00", "timestamp_unix": 1747094300, "type": "comment", "text": "@Sank362767403 @zaobaosg 你是没脸见人🤣", "context": {"type": "tweet", "id": "1922051695862775821", "text": "@zhongnanhaihu5 @zaobaosg 我不像殖畜一样自恋", "author_id": "563831315", "author_username": "Sank362767403"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922079565326799345", "timestamp": "2025-05-13T00:00:53+00:00", "timestamp_unix": 1747094453, "type": "comment", "text": "@xxxxcllllllll @wanganson421 @zaobaosg 不是你支偷偷豁免关税讨好美国", "context": {"type": "tweet", "id": "1921957570828292484", "text": "@zhongnanhaihu5 @wanganson421 @zaobaosg 降的是4月新增的关税，之前反制芬太尼的没有降。稀土管制，波音，大豆牛肉天然气等等也没有改变。好好弄清楚情况再来说，要不然显得很傻。", "author_id": "265601827", "author_username": "xxxxcllllllll"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 48}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922099542259892378", "timestamp": "2025-05-13T01:20:16+00:00", "timestamp_unix": 1747099216, "type": "comment", "text": "@gohun394600 @zaobaosg 不如你木高潮的猛🤣", "context": {"type": "tweet", "id": "1922085783592149073", "text": "@zhongnanhaihu5 @zaobaosg 殖畜又高潮了😄", "author_id": "1642425263198978048", "author_username": "gohun394600"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 45}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922099616230604800", "timestamp": "2025-05-13T01:20:34+00:00", "timestamp_unix": 1747099234, "type": "comment", "text": "@xxxxcllllllll @wanganson421 @zaobaosg 支就是你啊，吱吱作响", "context": {"type": "tweet", "id": "1922090019956593054", "text": "@zhongnanhaihu5 @wanganson421 @zaobaosg 照你这么说美国也偷偷豁免讨好中国，手机和电子产品之类的。另外，支是啥意思？", "author_id": "265601827", "author_username": "xxxxcllllllll"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 36}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922099874692022669", "timestamp": "2025-05-13T01:21:35+00:00", "timestamp_unix": 1747099295, "type": "comment", "text": "@AmyJohn92525208 @s5D7TYXFnEXOHg8 @HuangZu02699 @zaobaosg 工资是你木接一次客的价格🤣", "context": {"type": "tweet", "id": "1921966614326489601", "text": "@zhongnanhaihu5 @s5D7TYXFnEXOHg8 @HuangZu02699 @zaobaosg 工资多少？我分一半给你，让我也赚，表情包我也可以发😂", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922110024588919151", "timestamp": "2025-05-13T02:01:55+00:00", "timestamp_unix": 1747101715, "type": "comment", "text": "@s5D7TYXFnEXOHg8 @AmyJohn92525208 @HuangZu02699 @zaobaosg 你木初夜是不是被勾拿走了，还记得是你家哪条勾🤣", "context": {"type": "tweet", "id": "1922105266440487148", "text": "@zhongnanhaihu5 @AmyJohn92525208 @HuangZu02699 @zaobaosg 我工资直接是你母亲的一夜权，哈哈哈哈哈，她水多活好，说今年要给你要个弟弟妹妹", "author_id": "1301308754453958656", "author_username": "s5D7TYXFnEXOHg8"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922165245646123230", "timestamp": "2025-05-13T05:41:21+00:00", "timestamp_unix": 1747114881, "type": "comment", "text": "@wanganson421 @xxxxcllllllll @zaobaosg 这叫丑，那你什么神仙颜值😂", "context": {"type": "tweet", "id": "1922148570364297554", "text": "@xxxxcllllllll @zhongnanhaihu5 @zaobaosg 主要是他生得太丑了，啊哈哈哈 不得不恨自己父母和血统！", "author_id": "1835486598647193600", "author_username": "wanganson421"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922174964905476451", "timestamp": "2025-05-13T06:19:58+00:00", "timestamp_unix": 1747117198, "type": "comment", "text": "@wanganson421 @xxxxcllllllll @zaobaosg 我至少还五官端正，你别只喷不实干啊，拿出你的照片啊", "context": {"type": "tweet", "id": "1922165813135442342", "text": "@zhongnanhaihu5 @xxxxcllllllll @zaobaosg 这不叫丑？那你的宽容度提高啊！😉 斗鸡眼，塌鼻梁，撅撅嘴，组合在一起真的是绝了(👍ᐛ)", "author_id": "1835486598647193600", "author_username": "wanganson421"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1921865917241446533", "tweet_id": "1922175152403497319", "timestamp": "2025-05-13T06:20:43+00:00", "timestamp_unix": 1747117243, "type": "comment", "text": "@VWnFsve76nHtKDd @EileenM71244655 @fdsa753159 @bbcchinese 是你木给你口吗，还有你现在的样子重开多少回🤣", "context": {"type": "tweet", "id": "1922170817103691828", "text": "@zhongnanhaihu5 @EileenM71244655 @fdsa753159 @bbcchinese 我不用打的哈，一般都是别人给我口，说实话，我要长你这逼样我早重开了🤡", "author_id": "1470354935640621060", "author_username": "VWnFsve76nHtKDd"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 20}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922177895222423812", "timestamp": "2025-05-13T06:31:37+00:00", "timestamp_unix": 1747117897, "type": "comment", "text": "@lxdehao91 @birdyi23 @zaobaosg 支那人冷静一点🤣", "context": {"type": "tweet", "id": "1922175263393062945", "text": "@zhongnanhaihu5 @birdyi23 @zaobaosg 昨天你妈被我养的狗日了你知道吗，赶紧回去看看，你妈逼里还有狗的精液。", "author_id": "976305935244079104", "author_username": "lxdehao91"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 42}}, {"conversation_id": "1921865917241446533", "tweet_id": "1922178059903365373", "timestamp": "2025-05-13T06:32:16+00:00", "timestamp_unix": 1747117936, "type": "comment", "text": "@VWnFsve76nHtKDd @EileenM71244655 @fdsa753159 @bbcchinese 不是，就算你木给你口你也别不承认啊，她不给谁都口吗🤣", "context": {"type": "tweet", "id": "1922313502091248064", "text": "对于很多弱智在对中美关税谈判大放厥词，各自认为自己喜欢的一方“赢麻了”的表现，真的让人无语。大概是忘了川普搞所谓关税战到底是为了什么吧？制造业会因为给中国商品加了20%的关税，就被逼回美国吗？显然不会，那么这个关税谁来埋单？红脖子土鳖们，准备好了吗？", "author_id": "1109928797002821633", "author_username": "tonylusion1"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 18}}, {"conversation_id": "1921865917241446533", "tweet_id": "1922179685489754607", "timestamp": "2025-05-13T06:38:44+00:00", "timestamp_unix": 1747118324, "type": "comment", "text": "@VWnFsve76nHtKDd @EileenM71244655 @fdsa753159 @bbcchinese 好会说啊，小🕷️，可惜你木被口的时候只会咕噜咕噜🤣", "context": {"type": "tweet", "id": "1922178977424138317", "text": "@zhongnanhaihu5 @EileenM71244655 @fdsa753159 @bbcchinese 长的也畸形，脑子也畸形，能生你这种玩意的估计也不是什么正常生物，你木都一起重开算了，一家子残疾人🤗", "author_id": "1470354935640621060", "author_username": "VWnFsve76nHtKDd"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922184996065808442", "timestamp": "2025-05-13T06:59:50+00:00", "timestamp_unix": 1747119590, "type": "comment", "text": "@lxdehao91 @birdyi23 @zaobaosg 是我儿子的行了吧🤣", "context": {"type": "tweet", "id": "1922181553955738100", "text": "@zhongnanhaihu5 @birdyi23 @zaobaosg 你个杂种狗盗用谁的头像？你爸爸的吗？", "author_id": "976305935244079104", "author_username": "lxdehao91"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 35}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922196154726363269", "timestamp": "2025-05-13T07:44:10+00:00", "timestamp_unix": 1747122250, "type": "comment", "text": "@lxdehao91 @birdyi23 @zaobaosg 好像你有似得，我昨天在猪圈里看见的两百斤是你的🐴吗？😂", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 54}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922196316110700773", "timestamp": "2025-05-13T07:44:49+00:00", "timestamp_unix": 1747122289, "type": "comment", "text": "@14381438c @zaobaosg 女的看看b👀", "context": {"type": "tweet", "id": "1922188902070169856", "text": "@zhongnanhaihu5 @zaobaosg 我是女的 中肯评价 真的丑 😅", "author_id": "1833734566080806912", "author_username": "14381438c"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 15}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922210398989680805", "timestamp": "2025-05-13T08:40:46+00:00", "timestamp_unix": 1747125646, "type": "comment", "text": "@lxdehao91 @birdyi23 @zaobaosg 支那人冷静一下", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 30}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922210524839776662", "timestamp": "2025-05-13T08:41:16+00:00", "timestamp_unix": 1747125676, "type": "comment", "text": "@wanganson421 @xxxxcllllllll @zaobaosg 我长得吊打百分之九十九支那人", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 0, "quote_count": 0, "view_count": 27}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922220027513589963", "timestamp": "2025-05-13T09:19:02+00:00", "timestamp_unix": 1747127942, "type": "comment", "text": "@lxdehao91 @birdyi23 @zaobaosg 再不冷静就让皇军治疗治疗😆", "context": {"type": "tweet", "id": "1922219564483420347", "text": "@zhongnanhaihu5 @birdyi23 @zaobaosg 冷静你妈。", "author_id": "976305935244079104", "author_username": "lxdehao91"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 36}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922239770467369016", "timestamp": "2025-05-13T10:37:29+00:00", "timestamp_unix": 1747132649, "type": "comment", "text": "@wanganson421 @xxxxcllllllll @zaobaosg 我需要支那人投票？", "context": {"type": "tweet", "id": "1922239360465850867", "text": "@zhongnanhaihu5 @xxxxcllllllll @zaobaosg 你发个投票就知道了。丑逼 啊哈哈哈", "author_id": "1835486598647193600", "author_username": "wanganson421"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 24}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922256158707847359", "timestamp": "2025-05-13T11:42:36+00:00", "timestamp_unix": 1747136556, "type": "comment", "text": "@wanganson421 @xxxxcllllllll @zaobaosg 你说呢，支那人就是吱吱作响的呗😆", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 21}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922268351797071900", "timestamp": "2025-05-13T12:31:03+00:00", "timestamp_unix": 1747139463, "type": "comment", "text": "@14381438c @zaobaosg 我不配谁配", "context": {"type": "tweet", "id": "1922264240175641034", "text": "@zhongnanhaihu5 @zaobaosg 看看镜子里的你配吗？😅", "author_id": "1833734566080806912", "author_username": "14381438c"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 16}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922268547612282935", "timestamp": "2025-05-13T12:31:50+00:00", "timestamp_unix": 1747139510, "type": "comment", "text": "@lxdehao91 @birdyi23 @zaobaosg 支那人冷静点要不要皇军治你的偏头痛🤭", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 70}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922291748623012130", "timestamp": "2025-05-13T14:04:01+00:00", "timestamp_unix": 1747145041, "type": "comment", "text": "@xxxxcllllllll @wanganson421 @zaobaosg 是的，我是司马南", "context": {"type": "tweet", "id": "1922290658221441443", "text": "@zhongnanhaihu5 @wanganson421 @zaobaosg 你的脸真的好像被门挤过", "author_id": "265601827", "author_username": "xxxxcllllllll"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 23}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922652659472830838", "timestamp": "2025-05-14T13:58:09+00:00", "timestamp_unix": 1747231089, "type": "comment", "text": "@xing1202 @HollyHollo31469 @zaobaosg 每亩支畜又在唤🐴🤡", "context": {"type": "tweet", "id": "1922652135528808938", "text": "@zhongnanhaihu5 @HollyHollo31469 @zaobaosg 在你妈嘴里呢", "author_id": "1699954628228796416", "author_username": "xing1202"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 14}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922678308572987625", "timestamp": "2025-05-14T15:40:05+00:00", "timestamp_unix": 1747237205, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 看不懂是部分暂停，半导体都排除在外了，🕷️看得明白吗🤣", "context": {"type": "tweet", "id": "1922677109463405011", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 又来打假呱呱了，八囧交的吧，井底之蛙😂 https://t.co/vnmeuBeQUk", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 33}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922680302436049003", "timestamp": "2025-05-14T15:48:00+00:00", "timestamp_unix": 1747237680, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 怎么还问chatgpt啊🤣还有早在支那前就已经和其他国家一样暂缓九十天了", "context": {"type": "tweet", "id": "1922679466888732889", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 看懂了，特朗普政府未对台湾提供特殊待遇，😂这是谈判的结果啊？丢死人了 https://t.co/CsLzapih5H", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 39}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922681244237591010", "timestamp": "2025-05-14T15:51:44+00:00", "timestamp_unix": 1747237904, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 你支那天天就会偷台湾的芯片吧，就算送给美国也不给支那气不气😂", "context": {"type": "tweet", "id": "1922680785326223622", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 😂你美爹的产品不能用啊，同样90天就台湾送个台积电过去是吧😂表忠心❤️", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 42}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922682995225338207", "timestamp": "2025-05-14T15:58:42+00:00", "timestamp_unix": 1747238322, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 哎呀，好像你去了美国支那大使馆他们会理你一样，既然美国没给台湾建交，那为什么要给台湾免签呢😂", "context": {"type": "tweet", "id": "1922682327978627203", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 对啦，我们开航母去偷赖清德送货，巴不得全部送光光，最后我在美国连台湾大使馆都换不到，台湾人死活跟我又没关系，夜郎自大😂", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 43}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922803106846245362", "timestamp": "2025-05-14T23:55:59+00:00", "timestamp_unix": 1747266959, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 又赢麻了是吧，就对个“省”免签不对你支那免签还不是看你支那又穷又落后😂", "context": {"type": "tweet", "id": "1922684594408583510", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 这个问题让你美爹回答你，人家当你一个省不当你是国家😂 https://t.co/S2NiaXE1g5", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 34}}, {"conversation_id": "1921832491339465198", "tweet_id": "1922854167611076855", "timestamp": "2025-05-15T03:18:53+00:00", "timestamp_unix": 1747279133, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 全世界免签岛走不出去，你以为台湾是22年上海封控小区😂", "context": {"type": "tweet", "id": "1922841397876371614", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 有时候真的被你刘志鹏笑死，免不免签重要吗？大陆有实力的去香港澳门变个白色套想去就去，给你全世界免签，你还不是连个岛都走不出去，我去过台湾，你有来过大陆吗？免签哥😂", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 34}}, {"conversation_id": "1921832491339465198", "tweet_id": "1923165178947924367", "timestamp": "2025-05-15T23:54:43+00:00", "timestamp_unix": 1747353283, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 大陆不穷，你对比一下人均GDP好吧，你以为大陆到处都是上海，河南东北和上海能比？😂", "context": {"type": "tweet", "id": "1923037930211168530", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 我上面没一个字说走不出去，你好好学你的国文，我说有实力的不管在台湾还是大陆想出去都能出去，就怕你这种懂王，可能大陆都没来过的，还有啊大陆穷我不知道你是怎么想出来的，单拿一个上海你都呛了，好好学，你人生路还长😂", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 0, "quote_count": 0, "view_count": 44}}, {"conversation_id": "1921832491339465198", "tweet_id": "1923165310070173817", "timestamp": "2025-05-15T23:55:15+00:00", "timestamp_unix": 1747353315, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg china =chi +na you know？", "context": {"type": "tweet", "id": "1923040269127077910", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 这个👆🏻 https://t.co/WtFDlEzVhM", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 13}}, {"conversation_id": "1921832491339465198", "tweet_id": "1923288947574309042", "timestamp": "2025-05-16T08:06:32+00:00", "timestamp_unix": 1747382792, "type": "comment", "text": "@AmyJohn92525208 @eil45794574 @EileenM71244655 @zaobaosg 台湾跟国家有什么区别，况且共匪从来没统治过这里，还有别揣着明白装糊涂，你非要台湾跟上海北京比吗，也是，北京人是京爷😂", "context": {"type": "tweet", "id": "1923286578455085109", "text": "@zhongnanhaihu5 @eil45794574 @EileenM71244655 @zaobaosg 那你台湾有14亿人？省不和省比？😂你要是以国家比那就比国家GDP或者拿台北市来比大陆市，说人均你去看看台东县，用你的话台东县和台北能比😂", "author_id": "1614625384825507840", "author_username": "AmyJohn92525208"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 33}}]}