{"user_id": "1745291988164079616", "interactions_count": 18, "interactions": [{"conversation_id": "1909265096653652084", "tweet_id": "1909282575274873046", "timestamp": "2025-04-07T16:30:13+00:00", "timestamp_unix": 1744043413, "type": "comment", "text": "@garyccn1016 @BRICSinfo Trump’s purpose is to ask China to buy US debts, which has been reduced these year.", "context": {"type": "tweet", "id": "1909267866870026415", "text": "@BRICSinfo There is no way China will back down, Trump can simply open the door to let China buy 100 of billions worth of USD for Nvidia AI chips, then trade will be balanced very soon", "author_id": "44097819", "author_username": "ccng7619"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 97}}, {"conversation_id": "1909265096653652084", "tweet_id": "1909283052985237644", "timestamp": "2025-04-07T16:32:07+00:00", "timestamp_unix": 1744043527, "type": "comment", "text": "@atx_republican @BRICSinfo China will impose 50% back to US. You will see.", "context": {"type": "tweet", "id": "1909763709464903738", "text": "🧐👀🖕\nIt’s impossible, simply impossible 😌", "author_id": "1498844762580865047", "author_username": "Pbett73Patrick"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 197}}, {"conversation_id": "1911758840888021239", "tweet_id": "1912080899946254835", "timestamp": "2025-04-15T09:49:45+00:00", "timestamp_unix": 1744710585, "type": "comment", "text": "@profstonge Tough mouth but soft mind, just like your president, who has already backed down due to the horrific performance of the market.", "context": {"type": "tweet", "id": "1916159695884288021", "text": "Not to mention that the CCP central planners worst nightmare is having 10’s of millions unemployed, pissed off Chinese workers wandering in the streets. It would make <PERSON>’s revolution look like a picnic in comparison.", "author_id": "1599087751307046913", "author_username": "<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 9}}, {"conversation_id": "1912263329885585602", "tweet_id": "1912304944302170154", "timestamp": "2025-04-16T00:40:02+00:00", "timestamp_unix": 1744764002, "type": "comment", "text": "@ABCChinese 中国的想法也是一样的，“球在美国手中，美国需要和中国达成协议，我们则不一定要和他们达成协议”。所以，短期内，应该没什么协议了。静观其变吧～", "context": {"type": "tweet", "id": "1912895965708918863", "text": "你们美国都那么众星捧月了，干嘛操心中国的事。你美国自己玩就好了", "author_id": "1354595043936354305", "author_username": "tianjie86092931"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 105}}, {"conversation_id": "1912060960224719136", "tweet_id": "1912398328966566082", "timestamp": "2025-04-16T06:51:06+00:00", "timestamp_unix": 1744786266, "type": "comment", "text": "@zaobaosg 继续这么僵持下去，不仅中国会卖美债，其他国家也会的，资金永远是最聪明的。", "context": {"type": "tweet", "id": "1912364069778059416", "text": "我就想知道中国把美债全抛了会啥结果？", "author_id": "239740347", "author_username": "164081891"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 105}}, {"conversation_id": "1914859603059200252", "tweet_id": "1915072460825002175", "timestamp": "2025-04-23T15:57:09+00:00", "timestamp_unix": 1745423829, "type": "comment", "text": "@HeQinglian 不用深度怀疑，自从中国反击开始，所有的国家都是观望，在中美没有出结果之前，谁第一个签协议，谁就是SB。", "context": {"type": "tweet", "id": "1915041199947481204", "text": "中美之间选择已经不靠条约协议，川普第一届政府的第一阶段协议也没执行完。中国签署的协议不值得信任。美国只是在告诉大家，我要脱钩，你们也不用劝，只要选边就行。中美当前的协议也就是个眼前花安慰剂，谁也没那么认真。现在还没懂美国意图的，或者自持有能力两边得利的（欧盟），结局那就只有天知道", "author_id": "82369785", "author_username": "rainbear00"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 15, "quote_count": 0, "view_count": 1264}}, {"conversation_id": "1915337879389171888", "tweet_id": "1915369841797788113", "timestamp": "2025-04-24T11:38:50+00:00", "timestamp_unix": 1745494730, "type": "comment", "text": "@zaobaosg 谎言的艺术", "context": {"type": "tweet", "id": "1915538294722412544", "text": "撒谎的艺术？\n欺骗的艺术？\n摆烂的艺术？", "author_id": "41495294", "author_username": "shhgs"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 10, "quote_count": 1, "view_count": 2496}}, {"conversation_id": "1916315157640233470", "tweet_id": "1916350133211451638", "timestamp": "2025-04-27T04:34:10+00:00", "timestamp_unix": 1745728450, "type": "comment", "text": "@zaobaosg 从一个简单逻辑就可以知道有没有电话和会谈：现在会谈对谁有利，对谁不利。世界都在看中国和美国谈判的结果，中美没谈，他国就在拖。中国也没必要背着他国和美国谈。但美国需要有达成协议的样板，所以，到底是说在说谎，一目了然了。", "context": {"type": "tweet", "id": "1916518901648945546", "text": "不曾想，以前中国外交部常说的“美方只能是搬起石头砸自己的脚……”是一句预言。", "author_id": "811201583287439360", "author_username": "KUI__YANG"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 25, "quote_count": 0, "view_count": 1495}}, {"conversation_id": "1916643575577649414", "tweet_id": "1916647369497878980", "timestamp": "2025-04-28T00:15:17+00:00", "timestamp_unix": 1745799317, "type": "comment", "text": "@zaobaosg 有个疑问：所以现场就韩国媒体，世界各大媒体呢？", "context": {"type": "tweet", "id": "1916725095294489085", "text": "参照历史上的有些尿性，弄不好会签澶渊之盟或者广场协议之类的东西来牺牲中国普通人的利益。。。", "author_id": "615930407", "author_username": "971056667"}, "metrics": {"retweet_count": 1, "reply_count": 2, "like_count": 110, "quote_count": 0, "view_count": 1714}}, {"conversation_id": "1920573374100549727", "tweet_id": "1920641587391930532", "timestamp": "2025-05-09T00:46:52+00:00", "timestamp_unix": 1746751612, "type": "comment", "text": "@thecyrusjanssen Trump knows how to bend his knees when he realized that decoupling with China will hurt US more, especially MAGA more. He insists that he will not lower the tariffs to start the talk with China in front of the media. But he will do it secretly.", "context": {"type": "tweet", "id": "1920765097951023268", "text": "From “maximum pressure” to “managed retreat”—the tariff rollback underscores how economic realities trump rhetoric.", "author_id": "1252464973416030208", "author_username": "wangxh65"}, "metrics": {"retweet_count": 1, "reply_count": 0, "like_count": 2, "quote_count": 0, "view_count": 83}}, {"conversation_id": "1921832491339465198", "tweet_id": "1921853346320638349", "timestamp": "2025-05-12T09:01:58+00:00", "timestamp_unix": 1747040518, "type": "comment", "text": "@zaobaosg 所以，特朗普这一波搞到了啥？其他国家可以依照这个样板来谈了～", "context": {"type": "tweet", "id": "1921957944163188777", "text": "哇", "author_id": "1826961681886355456", "author_username": "luotutu111"}, "metrics": {"retweet_count": 0, "reply_count": 3, "like_count": 0, "quote_count": 0, "view_count": 4539}}, {"conversation_id": "1921837350549655769", "tweet_id": "1921855314355208425", "timestamp": "2025-05-12T09:09:47+00:00", "timestamp_unix": 1747040987, "type": "comment", "text": "@torontobigface 数学不好就不要出来瞎起哄了，容易打脸，到时候不是方脸，是圆脸。双方基本是回到了4.2之前。甚至芬太尼是否加征都不一定存在了。", "context": {"type": "tweet", "id": "1922095751598944545", "text": "实际情况。 https://t.co/KUJcSrA9vV", "author_id": "1681108678748049411", "author_username": "yue_yue19"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 89, "quote_count": 0, "view_count": 3137}}, {"conversation_id": "1921830983189647755", "tweet_id": "1921857288676999461", "timestamp": "2025-05-12T09:17:38+00:00", "timestamp_unix": 1747041458, "type": "comment", "text": "@thecyrusjanssen So the question is what <PERSON> has gained since his tariff policy on 4.2? And the rest of countries will follow the same way to negotiate now. They are waiting the final result of China-US negotiations.", "context": {"type": "tweet", "id": "1922212612953014292", "text": "@thecyrusjanssen evidencia al golfo de Trump.\nEl actual #POTUS es un negociante deshonesto, un mal jugador, tramposo e incapaz.\nUn rey desnudo que con su Prime Minister y su corte corrupta, pretende hacer creer al mundo que tiene la mejor ropa.\n🙏🏽🇨🇳\nhttps://t.co/rLuXeCEOYl", "author_id": "3025858259", "author_username": "napproach"}, "metrics": {"retweet_count": 0, "reply_count": 2, "like_count": 1, "quote_count": 0, "view_count": 952}}, {"conversation_id": "1921845596211630180", "tweet_id": "1921864116567736462", "timestamp": "2025-05-12T09:44:46+00:00", "timestamp_unix": 1747043086, "type": "comment", "text": "@mranti 赖清德跪着，被老特射了一脸，赖清德高呼：民主同盟！", "context": {"type": "tweet", "id": "1921958693081419840", "text": "所以美国对台湾现在关税比大陆高吗\n\n那么真的是实力打败了民主兄弟情啊\n\n川普太扎心了", "author_id": "1758514650776588288", "author_username": "ha<PERSON><PERSON><PERSON><PERSON>"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 480}}, {"conversation_id": "1921861095532191981", "tweet_id": "1921867469863453183", "timestamp": "2025-05-12T09:58:06+00:00", "timestamp_unix": 1747043886, "type": "comment", "text": "@torontobigface 通过中美关税计算，多伦多方脸变成了打脸，肿脸，傻脸！为川普洗地，洗到了自己的脸，脑子也进了水。真是可怜的小丑🤡", "context": {"type": "tweet", "id": "1921861095532191981", "text": "原始推文内容不可用", "author_id": "1477342523555135490", "author_username": "torontobigface"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 5, "quote_count": 0, "view_count": 675}}, {"conversation_id": "1921830983189647755", "tweet_id": "1921868074036175111", "timestamp": "2025-05-12T10:00:30+00:00", "timestamp_unix": 1747044030, "type": "comment", "text": "@Lasirois77 @thecyrusjanssen What he gained might be the stock trade on 4/9, he asked people to buy in and the market bounced back! I heard everyone made big money that day!", "context": {"type": "tweet", "id": "1921866921093341276", "text": "@realnelsonlin @thecyrusjanssen He gained nothing. Contracts are lost and not coming back. No new factories are coming to the US. China looks like a stable, reliable trade partner and the US is insane and unreasonable", "author_id": "799627507976851460", "author_username": "Lasirois77"}, "metrics": {"retweet_count": 0, "reply_count": 1, "like_count": 1, "quote_count": 0, "view_count": 147}}, {"conversation_id": "1921833860817428874", "tweet_id": "1921869291873300549", "timestamp": "2025-05-12T10:05:20+00:00", "timestamp_unix": 1747044320, "type": "comment", "text": "@yjpc007 懂王和他团队4/9交易赚了多少亿你知道吗？赚了他这辈子之前赚不到的钱！就一天哦！", "context": {"type": "tweet", "id": "1922100015675093140", "text": "这二爷总把自己当回事儿。指导川普如何理政。当年也许成功指导过败灯？", "author_id": "4086271932", "author_username": "jcweb11"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 1, "quote_count": 0, "view_count": 194}}, {"conversation_id": "1921830983189647755", "tweet_id": "1921878001676927114", "timestamp": "2025-05-12T10:39:57+00:00", "timestamp_unix": 1747046397, "type": "comment", "text": "@Jayconnor_C @Lasirois77 @thecyrusjanssen I mean everyone in his cabinet 😬", "context": {"type": "tweet", "id": "1921877170227466318", "text": "@realnelsonlin @Lasirois77 @thecyrusjanssen Yeah right everyone", "author_id": "1661809798462189569", "author_username": "Jayconnor_C"}, "metrics": {"retweet_count": 0, "reply_count": 0, "like_count": 0, "quote_count": 0, "view_count": 32}}]}